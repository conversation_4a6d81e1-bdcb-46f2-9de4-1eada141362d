module.exports = {
    apps: [
        {
            name: 'api-sports-game',
            script: 'dist/main.js',
            env: {
                APP_TYPE: 'api',
                TZ: 'UTC',
                NODE_TZ: 'UTC',
                NODE_ENV: 'production',
            },
            instances: 2,
            exec_mode: 'fork',
            watch: false,
            max_memory_restart: '10G',
        },
        {
            name: 'auto-update-sports-game',
            script: 'dist/worker.js',
            env: {
                APP_TYPE: 'worker',
                TZ: 'UTC',
                NODE_TZ: 'UTC',
                NODE_ENV: 'production',
            },
            instances: 1, // Chạy 2 worker để xử lý hàng đợi nhanh hơn
            exec_mode: 'fork',
            watch: false,
            max_memory_restart: '3G',
        },
    ],
};