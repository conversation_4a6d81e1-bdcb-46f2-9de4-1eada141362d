# API Service Environment Variables

# Server Configuration
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=sports_game

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# External API Configuration
API_FOOTBALL_URL=https://v3.football.api-sports.io
API_FOOTBALL_KEY=your_api_key_here

# Environment
NODE_ENV=development
