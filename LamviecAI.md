nguyên tắt làm việc AI.
1. <PERSON><PERSON><PERSON><PERSON> kế xong cấu trúc thư mục
2. <PERSON><PERSON><PERSON> truc DB cơ bản dự án
3. <PERSON><PERSON>n hỏi AI cần file nào trước khi code 1 chức năng.
4. <PERSON><PERSON> cấp files và nội dung: "Các file đang dùng"

Bắt đầu:
Tôi cần bạn kiểm tra mã nguồn của tôi, project_structure.txt là cấu trúc thư mực dự án. 
Cùng một source code, tôi đang chạy 2 service
- Servive  APISportsGame( main.ts): dùng để handle các HTTP request.
- service  AutoUpdateSportsGame ( worker.ts): tự động cập nhật data.
Kiềm tra cách import cảu tôi có tối ưu hay không?


Tạo folder tên: LogWorking, để lưu các tóm tắt nhừng gì đã hoàn thành, theo format
STT_gio_phut_ngay_thang_nam_tenchucnang.md và tạo doucment nếu có.
DEPLOYMENT_GUIDE.md
Read.me nội dung LogWorking - Tóm tắt công việc đã hoàn thành

Têu cầu AI tạo file rule để báo theo nguyên tắt code ( .augment-rules.md)