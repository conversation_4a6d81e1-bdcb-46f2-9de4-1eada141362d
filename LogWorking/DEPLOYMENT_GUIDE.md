# APISportsGamev2 - Deployment Guide

## 🏗️ **Architecture Overview**

Dự án được tách thành 2 services độc lập:
- **API Service**: Handle HTTP requests, REST endpoints
- **Worker Service**: Background sync, cron jobs, queue processing

## 🚀 **Quick Start**

### **Development**

1. **Setup Environment**
```bash
# Copy environment files
cp .env.api.example .env.api
cp .env.worker.example .env.worker

# Edit với thông tin database và API keys
```

2. **Install Dependencies**
```bash
npm install
```

3. **Start Services**
```bash
# Terminal 1 - API Service
npm run start:api:dev

# Terminal 2 - Worker Service  
npm run start:worker:dev
```

### **Production với Docker**

1. **Build và Start**
```bash
docker-compose up -d
```

2. **Check Status**
```bash
docker-compose ps
docker-compose logs api
docker-compose logs worker
```

## 📋 **Available Scripts**

### **API Service**
```bash
npm run start:api          # Start API (production)
npm run start:api:dev      # Start API (development)
npm run start:api:prod     # Start API (production build)
```

### **Worker Service**
```bash
npm run start:worker       # Start Worker (production)
npm run start:worker:dev   # Start Worker (development)  
npm run start:worker:prod  # Start Worker (production build)
```

### **Build & Test**
```bash
npm run build             # Build both services
npm run build:api         # Build API only
npm run build:worker      # Build Worker only
npm test                  # Run tests
```

## 🔧 **Configuration**

### **API Service (.env.api)**
```env
# Server
PORT=3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=sports_game

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# External API
API_FOOTBALL_URL=https://v3.football.api-sports.io
API_FOOTBALL_KEY=your_api_key
```

### **Worker Service (.env.worker)**
```env
# Database (same as API)
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=sports_game

# Redis (same as API)
REDIS_HOST=localhost
REDIS_PORT=6379

# Worker-specific
SYNC_BATCH_SIZE=100
MAX_IDS_PER_REQUEST=20
MAX_CONCURRENT_REQUESTS=1
API_RATE_LIMIT=100
```

## 🐳 **Docker Deployment**

### **Single Machine**
```bash
# Start all services
docker-compose up -d

# Scale API service
docker-compose up -d --scale api=3

# View logs
docker-compose logs -f api
docker-compose logs -f worker
```

### **Separate Machines**

#### **API Server**
```bash
# Build API image
docker build -f Dockerfile.api -t sports-api .

# Run API container
docker run -d \
  --name sports-api \
  -p 3000:3000 \
  --env-file .env.api \
  sports-api
```

#### **Worker Server**
```bash
# Build Worker image
docker build -f Dockerfile.worker -t sports-worker .

# Run Worker container
docker run -d \
  --name sports-worker \
  --env-file .env.worker \
  sports-worker
```

## 📊 **Monitoring**

### **Health Checks**

#### **API Service**
```bash
curl http://localhost:3000/football/fixtures/sync/status
```

#### **Worker Service**
```bash
# Check logs for cron job execution
docker logs sports-worker | grep "Starting live fixtures sync"
```

### **Database Monitoring**
```sql
-- Check recent fixtures
SELECT COUNT(*) FROM fixture WHERE date >= NOW() - INTERVAL '1 day';

-- Check sync status
SELECT * FROM fixture ORDER BY updated_at DESC LIMIT 10;
```

## 🔄 **Scaling Strategies**

### **API Service Scaling**
```bash
# Horizontal scaling với load balancer
docker-compose up -d --scale api=5

# Hoặc với Kubernetes
kubectl scale deployment api-deployment --replicas=5
```

### **Worker Service Scaling**
```bash
# Vertical scaling (tăng resources)
docker run --memory=2g --cpus=2 sports-worker

# Multiple workers cho different tasks
docker run -e WORKER_TYPE=sync sports-worker
docker run -e WORKER_TYPE=stats sports-worker
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **API không start được**
```bash
# Check logs
npm run start:api:dev

# Common fixes:
# 1. Check database connection
# 2. Verify port 3000 is free
# 3. Check .env.api file
```

#### **Worker không sync data**
```bash
# Check worker logs
docker logs sports-worker

# Common fixes:
# 1. Check API_FOOTBALL_KEY
# 2. Verify Redis connection
# 3. Check database permissions
```

#### **Database connection issues**
```bash
# Test database connection
psql -h localhost -U postgres -d sports_game

# Check if database exists
docker exec -it postgres psql -U postgres -l
```

## 📈 **Performance Optimization**

### **API Service**
- Enable Redis caching
- Use connection pooling
- Implement rate limiting
- Add CDN for static assets

### **Worker Service**  
- Optimize batch sizes
- Implement queue priorities
- Add retry mechanisms
- Monitor API rate limits

## 🔐 **Security**

### **Environment Variables**
- Never commit .env files
- Use secrets management in production
- Rotate API keys regularly

### **Database**
- Use strong passwords
- Enable SSL connections
- Restrict network access
- Regular backups

## 📝 **Maintenance**

### **Regular Tasks**
```bash
# Update dependencies
npm update

# Clean old data
# (Add cleanup scripts as needed)

# Backup database
pg_dump sports_game > backup.sql
```

### **Monitoring Checklist**
- [ ] API response times
- [ ] Worker job completion rates
- [ ] Database performance
- [ ] Redis memory usage
- [ ] External API rate limits
