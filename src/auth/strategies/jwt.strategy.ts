import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemUser } from '../entities/system-user.entity';
import { SystemUserJwtPayload, UserType } from '../types/auth.types';

/**
 * JWT Strategy for validating access tokens
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
    constructor(
        private configService: ConfigService,
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>('JWT_SECRET', 'default-jwt-secret'),
        });
    }

    /**
     * Validate JWT payload and return user context
     */
    async validate(payload: SystemUserJwtPayload): Promise<SystemUser> {
        // Validate payload structure
        if (!payload.sub || !payload.userType) {
            throw new UnauthorizedException('Invalid token payload');
        }

        // Currently only supporting system users
        if (payload.userType !== UserType.SYSTEM) {
            throw new UnauthorizedException('Unsupported user type');
        }

        // Find user in database
        const user = await this.systemUserRepository.findOne({
            where: { 
                id: payload.sub,
                isActive: true 
            }
        });

        if (!user) {
            throw new UnauthorizedException('User not found or inactive');
        }

        // Verify role matches token
        if (user.role !== payload.role) {
            throw new UnauthorizedException('User role mismatch');
        }

        // Update last login time
        user.lastLoginAt = new Date();
        await this.systemUserRepository.save(user);

        return user;
    }
}
