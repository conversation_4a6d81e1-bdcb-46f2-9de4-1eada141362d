import {
    Controller,
    Post,
    Get,
    Body,
    HttpCode,
    HttpStatus,
    UseGuards,
    Request,
    Logger,
    Ip,
    Headers,
    UnauthorizedException
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AuthService } from '../services/auth.service';
import { UserService } from '../services/user.service';
import {
    SystemUserLoginDto,
    SystemUserCreateDto,
    RefreshTokenDto,
    AuthResponseDto,
    UserProfileDto,
    DeviceInfoDto
} from '../dto/auth.dto';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { RolesGuard } from '../guards/roles.guard';
import { Public, CurrentUser, AdminOnly, Roles } from '../decorators/auth.decorators';
import { LoginRateLimit, RegisterRateLimit, RefreshRateLimit, AdminRateLimit } from '../decorators/rate-limit.decorators';
import { AuthRateLimitGuard } from '../guards/rate-limit.guard';
import { SystemUser } from '../entities/system-user.entity';
import { SystemRole } from '../types/auth.types';

/**
 * Authentication Controller
 * Handles system user authentication endpoints
 */
@Controller('auth')
@UseGuards(JwtAuthGuard, RolesGuard, AuthRateLimitGuard)
export class AuthController {
    private readonly logger = new Logger(AuthController.name);

    constructor(
        private readonly authService: AuthService,
        private readonly userService: UserService,
        @InjectRepository(SystemUser)
        private readonly systemUserRepository: Repository<SystemUser>,
    ) { }

    /**
     * System user login
     * POST /auth/login
     */
    @Public()
    @LoginRateLimit()
    @Post('login')
    @HttpCode(HttpStatus.OK)
    async login(
        @Body() loginDto: SystemUserLoginDto,
        @Ip() ipAddress: string,
        @Headers('user-agent') userAgent: string,
    ): Promise<AuthResponseDto> {
        this.logger.log(`Login attempt for user: ${loginDto.username} from IP: ${ipAddress}`);

        // Create device info
        const deviceInfo: DeviceInfoDto = {
            ipAddress,
            userAgent,
            deviceInfo: `${userAgent?.split(' ')[0] || 'Unknown'} from ${ipAddress}`,
        };

        // Authenticate user
        const tokens = await this.authService.loginSystemUser(loginDto, deviceInfo);

        // Get user by username (since we just authenticated)
        const user = await this.systemUserRepository.findOne({
            where: { username: loginDto.username, isActive: true }
        });

        if (!user) {
            throw new UnauthorizedException('User not found after authentication');
        }

        this.logger.log(`User ${loginDto.username} logged in successfully`);

        return {
            accessToken: tokens.accessToken,
            refreshToken: tokens.refreshToken,
            user: {
                id: user.id,
                username: user.username,
                email: user.email,
                role: user.role as SystemRole,
                fullName: user.fullName,
            },
        };
    }

    /**
     * Admin registration (protected - admin only)
     * POST /auth/admin/register
     */
    @AdminOnly()
    @RegisterRateLimit()
    @Post('admin/register')
    @HttpCode(HttpStatus.CREATED)
    async registerAdmin(
        @Body() createDto: SystemUserCreateDto,
        @CurrentUser() currentUser: SystemUser,
    ): Promise<{ message: string; user: UserProfileDto }> {
        this.logger.log(`Admin registration attempt by ${currentUser.username} for user: ${createDto.username}`);

        const user = await this.authService.createSystemUser(createDto, currentUser.id);
        const userProfile = await this.userService.getUserProfile(user.id);

        this.logger.log(`Admin user ${createDto.username} created successfully by ${currentUser.username}`);

        return {
            message: 'System user created successfully',
            user: userProfile,
        };
    }

    /**
     * Refresh access token
     * POST /auth/refresh
     */
    @Public()
    @RefreshRateLimit()
    @Post('refresh')
    @HttpCode(HttpStatus.OK)
    async refresh(@Body() refreshDto: RefreshTokenDto): Promise<{ accessToken: string }> {
        this.logger.debug('Token refresh attempt');

        const result = await this.authService.refreshAccessToken(refreshDto.refreshToken);

        this.logger.debug('Token refreshed successfully');
        return result;
    }

    /**
     * Get current user profile
     * GET /auth/profile
     */
    @Get('profile')
    async getProfile(@CurrentUser() user: SystemUser): Promise<UserProfileDto> {
        return this.userService.getUserProfile(user.id);
    }

    /**
     * Logout current session
     * POST /auth/logout
     */
    @Post('logout')
    @HttpCode(HttpStatus.OK)
    async logout(
        @Body() refreshDto: RefreshTokenDto,
        @CurrentUser() user: SystemUser,
    ): Promise<{ message: string }> {
        this.logger.log(`Logout attempt for user: ${user.username}`);

        await this.authService.logout(refreshDto.refreshToken);

        this.logger.log(`User ${user.username} logged out successfully`);
        return { message: 'Logged out successfully' };
    }

    /**
     * Logout from all devices
     * POST /auth/logout-all
     */
    @Post('logout-all')
    @HttpCode(HttpStatus.OK)
    async logoutAll(@CurrentUser() user: SystemUser): Promise<{ message: string }> {
        this.logger.log(`Logout all devices for user: ${user.username}`);

        await this.authService.logoutFromAllDevices(user.id);

        this.logger.log(`User ${user.username} logged out from all devices`);
        return { message: 'Logged out from all devices successfully' };
    }

    /**
     * Get user sessions (current user only)
     * GET /auth/sessions
     */
    @Get('sessions')
    async getSessions(@CurrentUser() user: SystemUser) {
        return this.userService.getUserSessions(user.id);
    }

    /**
     * Revoke specific session
     * POST /auth/sessions/:sessionId/revoke
     */
    @Post('sessions/:sessionId/revoke')
    @HttpCode(HttpStatus.OK)
    async revokeSession(
        @CurrentUser() user: SystemUser,
        @Body('sessionId') sessionId: number,
    ): Promise<{ message: string }> {
        await this.userService.revokeSession(user.id, sessionId);
        return { message: 'Session revoked successfully' };
    }

    /**
     * Admin: Get all system users
     * GET /auth/admin/users
     */
    @AdminOnly()
    @Get('admin/users')
    async getAllUsers(): Promise<UserProfileDto[]> {
        return this.userService.getAllSystemUsers();
    }

    /**
     * Admin: Update user status
     * POST /auth/admin/users/:userId/status
     */
    @AdminOnly()
    @Post('admin/users/:userId/status')
    @HttpCode(HttpStatus.OK)
    async updateUserStatus(
        @Body('userId') userId: number,
        @Body('isActive') isActive: boolean,
        @CurrentUser() currentUser: SystemUser,
    ): Promise<{ message: string }> {
        this.logger.log(`Admin ${currentUser.username} updating user ${userId} status to ${isActive}`);

        await this.userService.updateUserStatus(userId, isActive);
        return { message: 'User status updated successfully' };
    }

    /**
     * Admin: Update user role
     * POST /auth/admin/users/:userId/role
     */
    @AdminOnly()
    @Post('admin/users/:userId/role')
    @HttpCode(HttpStatus.OK)
    async updateUserRole(
        @Body('userId') userId: number,
        @Body('role') role: SystemRole,
        @CurrentUser() currentUser: SystemUser,
    ): Promise<{ message: string }> {
        this.logger.log(`Admin ${currentUser.username} updating user ${userId} role to ${role}`);

        await this.userService.updateUserRole(userId, role);
        return { message: 'User role updated successfully' };
    }

    /**
     * Admin: Get user statistics
     * GET /auth/admin/stats
     */
    @AdminOnly()
    @Get('admin/stats')
    async getUserStats() {
        return this.userService.getUserStats();
    }

    /**
     * Force create default admin (for testing only)
     * POST /auth/force-create-admin
     */
    @Public()
    @Post('force-create-admin')
    @HttpCode(HttpStatus.CREATED)
    async forceCreateAdmin(): Promise<{ message: string; credentials: any }> {
        this.logger.log('Force creating default admin user');

        // Delete existing admin if exists
        await this.systemUserRepository.delete({ username: 'admin' });

        const createDto: SystemUserCreateDto = {
            username: 'admin',
            email: '<EMAIL>',
            password: 'admin123456',
            fullName: 'System Administrator',
            role: SystemRole.ADMIN,
        };

        const user = await this.authService.createSystemUser(createDto);

        this.logger.log('Default admin user force created successfully');

        return {
            message: 'Default admin user created successfully',
            credentials: {
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin123456',
                role: 'admin',
            },
        };
    }


}
