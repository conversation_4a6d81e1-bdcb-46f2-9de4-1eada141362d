import { SetMetadata, createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SystemRole } from '../types/auth.types';
import { SystemUser } from '../entities/system-user.entity';

/**
 * Public decorator - marks routes as public (no authentication required)
 */
export const Public = () => SetMetadata('isPublic', true);

/**
 * Roles decorator - specifies required roles for accessing endpoint
 */
export const Roles = (...roles: SystemRole[]) => SetMetadata('roles', roles);

/**
 * CurrentUser decorator - extracts current user from request
 */
export const CurrentUser = createParamDecorator(
    (data: unknown, ctx: ExecutionContext): SystemUser => {
        const request = ctx.switchToHttp().getRequest();
        return request.user;
    },
);

/**
 * Admin Only decorator - shorthand for admin role requirement
 */
export const AdminOnly = () => Roles(SystemRole.ADMIN);

/**
 * Editor Plus decorator - allows admin and editor roles
 */
export const EditorPlus = () => Roles(SystemRole.ADMIN, SystemRole.EDITOR);

/**
 * Moderator Plus decorator - allows admin and moderator roles
 */
export const ModeratorPlus = () => Roles(SystemRole.ADMIN, SystemRole.MODERATOR);
