import { Entity, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { SystemUser } from './system-user.entity';

/**
 * Refresh Token Entity
 * Stores refresh tokens for revocation capability
 */
@Entity('refresh_tokens')
export class RefreshToken {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({ unique: true })
    token: string;

    @Index()
    @Column()
    userId: number;

    @Column({ type: 'enum', enum: ['system', 'registered'], default: 'system' })
    userType: 'system' | 'registered';

    @Column({ nullable: true })
    deviceInfo: string;

    @Column({ nullable: true })
    ipAddress: string;

    @Column({ nullable: true })
    userAgent: string;

    @Column({ type: 'timestamp with time zone' })
    expiresAt: Date;

    @Column({ default: false })
    isRevoked: boolean;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    revokedAt: Date;

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    // Relations
    @ManyToOne(() => SystemUser, { onDelete: 'CASCADE' })
    @JoinColumn({ name: 'userId' })
    systemUser: SystemUser;

    /**
     * Check if token is expired
     */
    isExpired(): boolean {
        return new Date() > this.expiresAt;
    }

    /**
     * Check if token is valid (not expired and not revoked)
     */
    isValid(): boolean {
        return !this.isExpired() && !this.isRevoked;
    }

    /**
     * Revoke the token
     */
    revoke(): void {
        this.isRevoked = true;
        this.revokedAt = new Date();
    }
}
