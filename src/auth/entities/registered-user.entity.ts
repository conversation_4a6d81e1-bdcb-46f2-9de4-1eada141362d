import { Entity, Column, PrimaryGeneratedColumn, Index, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Registered User Entity
 * For end users who register through the application
 * Note: Currently not in use, focusing on SystemUser only
 */
@Entity('registered_users')
export class RegisteredUser {
    @PrimaryGeneratedColumn()
    id: number;

    @Index()
    @Column({ unique: true })
    username: string;

    @Index()
    @Column({ unique: true })
    email: string;

    @Column()
    passwordHash: string;

    @Column({ nullable: true })
    fullName: string;

    @Column({ nullable: true })
    firstName: string;

    @Column({ nullable: true })
    lastName: string;

    @Column({ nullable: true })
    displayName: string;

    @Column({ type: 'enum', enum: ['user', 'premium'], default: 'user' })
    userType: 'user' | 'premium';

    @Column({ default: true })
    isActive: boolean;

    @Column({ default: false })
    isEmailVerified: boolean;

    @Column({ nullable: true })
    emailVerificationToken: string;

    @Column({ nullable: true })
    passwordResetToken: string;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    passwordResetExpires: Date;

    @Column({ nullable: true, type: 'timestamp with time zone' })
    lastLoginAt: Date;

    @CreateDateColumn({ type: 'timestamp with time zone' })
    createdAt: Date;

    @UpdateDateColumn({ type: 'timestamp with time zone' })
    updatedAt: Date;

    /**
     * Get full name of the user
     */
    getFullName(): string {
        if (this.firstName && this.lastName) {
            return `${this.firstName} ${this.lastName}`;
        }
        return this.displayName || this.email;
    }

    /**
     * Check if user has premium access
     */
    hasPremiumAccess(): boolean {
        return this.userType === 'premium' && this.isActive;
    }
}
