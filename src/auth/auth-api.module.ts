import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AuthModule } from './auth.module';
import { AuthController } from './controllers/auth.controller';
import { SystemUser } from './entities/system-user.entity';

/**
 * Auth API Module
 * Exposes authentication endpoints for the API service
 */
@Module({
    imports: [
        AuthModule,
        TypeOrmModule.forFeature([SystemUser]),
    ],
    controllers: [AuthController],
})
export class AuthApiModule { }
