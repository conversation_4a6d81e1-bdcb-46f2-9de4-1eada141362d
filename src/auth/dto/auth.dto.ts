import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>E<PERSON> } from 'class-validator';
import { SystemRole } from '../types/auth.types';

/**
 * System User Login DTO
 */
export class SystemUserLoginDto {
    @IsString()
    @MinLength(3)
    @MaxLength(50)
    username: string;

    @IsString()
    @MinLength(6)
    password: string;
}

/**
 * System User Registration DTO
 */
export class SystemUserCreateDto {
    @IsString()
    @MinLength(3)
    @MaxLength(50)
    username: string;

    @IsEmail()
    email: string;

    @IsString()
    @MinLength(8)
    @MaxLength(128)
    password: string;

    @IsOptional()
    @IsString()
    @MaxLength(100)
    fullName?: string;

    @IsEnum(SystemRole)
    role: SystemRole;
}

/**
 * Refresh Token DTO
 */
export class RefreshTokenDto {
    @IsString()
    refreshToken: string;
}

/**
 * Auth Response DTO
 */
export class AuthResponseDto {
    accessToken: string;
    refreshToken: string;
    user: {
        id: number;
        username: string;
        email: string;
        role: SystemRole;
        fullName?: string;
    };
}

/**
 * Token Pair DTO
 */
export class TokenPairDto {
    accessToken: string;
    refreshToken: string;
}

/**
 * User Profile Response DTO
 */
export class UserProfileDto {
    id: number;
    username: string;
    email: string;
    fullName?: string;
    role: SystemRole;
    isActive: boolean;
    lastLoginAt?: Date;
    createdAt: Date;
}

/**
 * Device Info DTO for tracking refresh tokens
 */
export class DeviceInfoDto {
    @IsOptional()
    @IsString()
    deviceInfo?: string;

    @IsOptional()
    @IsString()
    userAgent?: string;

    @IsOptional()
    @IsString()
    ipAddress?: string;
}
