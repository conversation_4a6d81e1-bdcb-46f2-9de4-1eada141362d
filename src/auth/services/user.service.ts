import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemUser } from '../entities/system-user.entity';
import { RefreshToken } from '../entities/refresh-token.entity';
import { UserProfileDto } from '../dto/auth.dto';
import { SystemRole } from '../types/auth.types';

/**
 * User Service
 * Handles user management operations
 */
@Injectable()
export class UserService {
    private readonly logger = new Logger(UserService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        @InjectRepository(RefreshToken)
        private refreshTokenRepository: Repository<RefreshToken>,
    ) {}

    /**
     * Get user profile by ID
     */
    async getUserProfile(userId: number): Promise<UserProfileDto> {
        const user = await this.systemUserRepository.findOne({
            where: { id: userId, isActive: true }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        return {
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role as SystemRole,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
        };
    }

    /**
     * Get all system users (admin only)
     */
    async getAllSystemUsers(): Promise<UserProfileDto[]> {
        const users = await this.systemUserRepository.find({
            order: { createdAt: 'DESC' }
        });

        return users.map(user => ({
            id: user.id,
            username: user.username,
            email: user.email,
            fullName: user.fullName,
            role: user.role as SystemRole,
            isActive: user.isActive,
            lastLoginAt: user.lastLoginAt,
            createdAt: user.createdAt,
        }));
    }

    /**
     * Update user status (activate/deactivate)
     */
    async updateUserStatus(userId: number, isActive: boolean): Promise<void> {
        const user = await this.systemUserRepository.findOne({
            where: { id: userId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        user.isActive = isActive;
        await this.systemUserRepository.save(user);

        // If deactivating, revoke all refresh tokens
        if (!isActive) {
            await this.refreshTokenRepository.update(
                { userId, isRevoked: false },
                { isRevoked: true, revokedAt: new Date() }
            );
        }

        this.logger.log(`User ${user.username} ${isActive ? 'activated' : 'deactivated'}`);
    }

    /**
     * Update user role (admin only)
     */
    async updateUserRole(userId: number, newRole: SystemRole): Promise<void> {
        const user = await this.systemUserRepository.findOne({
            where: { id: userId }
        });

        if (!user) {
            throw new NotFoundException('User not found');
        }

        const oldRole = user.role;
        user.role = newRole;
        await this.systemUserRepository.save(user);

        this.logger.log(`User ${user.username} role changed from ${oldRole} to ${newRole}`);
    }

    /**
     * Get user's active sessions (refresh tokens)
     */
    async getUserSessions(userId: number): Promise<any[]> {
        const sessions = await this.refreshTokenRepository.find({
            where: { userId, isRevoked: false },
            order: { createdAt: 'DESC' }
        });

        return sessions.map(session => ({
            id: session.id,
            deviceInfo: session.deviceInfo,
            ipAddress: session.ipAddress,
            userAgent: session.userAgent,
            createdAt: session.createdAt,
            expiresAt: session.expiresAt,
            isExpired: session.isExpired(),
        }));
    }

    /**
     * Revoke specific session
     */
    async revokeSession(userId: number, sessionId: number): Promise<void> {
        const session = await this.refreshTokenRepository.findOne({
            where: { id: sessionId, userId }
        });

        if (!session) {
            throw new NotFoundException('Session not found');
        }

        session.revoke();
        await this.refreshTokenRepository.save(session);

        this.logger.log(`Session ${sessionId} revoked for user ${userId}`);
    }

    /**
     * Get user statistics
     */
    async getUserStats(): Promise<any> {
        const totalUsers = await this.systemUserRepository.count();
        const activeUsers = await this.systemUserRepository.count({
            where: { isActive: true }
        });
        const adminUsers = await this.systemUserRepository.count({
            where: { role: SystemRole.ADMIN, isActive: true }
        });
        const activeSessions = await this.refreshTokenRepository.count({
            where: { isRevoked: false }
        });

        return {
            totalUsers,
            activeUsers,
            adminUsers,
            activeSessions,
            inactiveUsers: totalUsers - activeUsers,
        };
    }
}
