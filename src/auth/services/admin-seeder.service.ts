import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { SystemUser } from '../entities/system-user.entity';
import { SystemRole } from '../types/auth.types';

/**
 * Admin Seeder Service
 * Creates default admin user if none exists
 */
@Injectable()
export class AdminSeederService implements OnModuleInit {
    private readonly logger = new Logger(AdminSeederService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        private configService: ConfigService,
    ) { }

    /**
     * Create default admin user on module initialization
     */
    async onModuleInit() {
        await this.createDefaultAdmin();
    }

    /**
     * Create default admin user if none exists
     */
    async createDefaultAdmin(): Promise<void> {
        try {
            // Check if default admin user exists
            const existingAdmin = await this.systemUserRepository.findOne({
                where: { username: 'admin' }
            });

            if (existingAdmin) {
                this.logger.log('Default admin user already exists, skipping seeder');
                this.logger.log('📧 Email: <EMAIL>');
                this.logger.log('👤 Username: admin');
                this.logger.log('🔑 Use existing password or reset if needed');
                return;
            }

            // Create default admin user
            const defaultAdmin = {
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin123456', // Change this in production!
                fullName: 'System Administrator',
                role: SystemRole.ADMIN,
            };

            // Hash password
            const passwordHash = await bcrypt.hash(defaultAdmin.password, 12);

            // Create admin user
            const adminUser = this.systemUserRepository.create({
                username: defaultAdmin.username,
                email: defaultAdmin.email,
                passwordHash,
                fullName: defaultAdmin.fullName,
                role: defaultAdmin.role,
                createdBy: undefined, // Self-created
            });

            await this.systemUserRepository.save(adminUser);

            this.logger.log('🎉 Default admin user created successfully!');
            this.logger.log('📧 Email: <EMAIL>');
            this.logger.log('👤 Username: admin');
            this.logger.log('🔑 Password: admin123456');
            this.logger.warn('⚠️  Please change the default password in production!');

        } catch (error) {
            this.logger.error(`Failed to create default admin user: ${error.message}`);
        }
    }

    /**
     * Create additional admin user (for manual use)
     */
    async createAdminUser(
        username: string,
        email: string,
        password: string,
        fullName?: string,
        createdBy?: number
    ): Promise<SystemUser> {
        // Check if username already exists
        const existingUsername = await this.systemUserRepository.findOne({
            where: { username }
        });
        if (existingUsername) {
            throw new Error('Username already exists');
        }

        // Check if email already exists
        const existingEmail = await this.systemUserRepository.findOne({
            where: { email }
        });
        if (existingEmail) {
            throw new Error('Email already exists');
        }

        // Hash password
        const passwordHash = await bcrypt.hash(password, 12);

        // Create admin user
        const adminUser = this.systemUserRepository.create({
            username,
            email,
            passwordHash,
            fullName,
            role: SystemRole.ADMIN,
            createdBy,
        });

        const savedUser = await this.systemUserRepository.save(adminUser);
        this.logger.log(`Admin user ${username} created successfully`);

        return savedUser;
    }

    /**
     * Reset admin password (for emergency use)
     */
    async resetAdminPassword(username: string, newPassword: string): Promise<void> {
        const admin = await this.systemUserRepository.findOne({
            where: { username, role: SystemRole.ADMIN }
        });

        if (!admin) {
            throw new Error('Admin user not found');
        }

        // Hash new password
        const passwordHash = await bcrypt.hash(newPassword, 12);

        // Update password
        admin.passwordHash = passwordHash;
        await this.systemUserRepository.save(admin);

        this.logger.log(`Password reset for admin user ${username}`);
    }

    /**
     * Get admin user statistics
     */
    async getAdminStats(): Promise<any> {
        const totalAdmins = await this.systemUserRepository.count({
            where: { role: SystemRole.ADMIN }
        });

        const activeAdmins = await this.systemUserRepository.count({
            where: { role: SystemRole.ADMIN, isActive: true }
        });

        const recentAdmins = await this.systemUserRepository.find({
            where: { role: SystemRole.ADMIN },
            order: { lastLoginAt: 'DESC' },
            take: 5,
            select: ['id', 'username', 'email', 'lastLoginAt', 'isActive']
        });

        return {
            totalAdmins,
            activeAdmins,
            inactiveAdmins: totalAdmins - activeAdmins,
            recentAdmins,
        };
    }
}
