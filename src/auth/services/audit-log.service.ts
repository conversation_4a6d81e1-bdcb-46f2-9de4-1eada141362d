import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemUser } from '../entities/system-user.entity';

/**
 * Audit action types
 */
export enum AuditAction {
    LOGIN = 'LOGIN',
    LOGOUT = 'LOGOUT',
    LOGOUT_ALL = 'LOGOUT_ALL',
    REFRESH_TOKEN = 'REFRESH_TOKEN',
    CREATE_USER = 'CREATE_USER',
    UPDATE_USER_STATUS = 'UPDATE_USER_STATUS',
    UPDATE_USER_ROLE = 'UPDATE_USER_ROLE',
    REVOKE_SESSION = 'REVOKE_SESSION',
    FAILED_LOGIN = 'FAILED_LOGIN',
    RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
}

/**
 * Audit log entry interface
 */
export interface AuditLogEntry {
    action: AuditAction;
    userId?: number;
    username?: string;
    ipAddress?: string;
    userAgent?: string;
    details?: any;
    timestamp: Date;
    success: boolean;
    errorMessage?: string;
}

/**
 * Audit Logging Service
 * Tracks all authentication and authorization events
 */
@Injectable()
export class AuditLogService {
    private readonly logger = new Logger(AuditLogService.name);

    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
    ) {}

    /**
     * Log successful login
     */
    async logLogin(user: SystemUser, ipAddress?: string, userAgent?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.LOGIN,
            userId: user.id,
            username: user.username,
            ipAddress,
            userAgent,
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log failed login attempt
     */
    async logFailedLogin(username: string, ipAddress?: string, userAgent?: string, reason?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.FAILED_LOGIN,
            username,
            ipAddress,
            userAgent,
            timestamp: new Date(),
            success: false,
            errorMessage: reason,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log logout
     */
    async logLogout(user: SystemUser, ipAddress?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.LOGOUT,
            userId: user.id,
            username: user.username,
            ipAddress,
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log logout from all devices
     */
    async logLogoutAll(user: SystemUser, ipAddress?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.LOGOUT_ALL,
            userId: user.id,
            username: user.username,
            ipAddress,
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log token refresh
     */
    async logTokenRefresh(userId: number, ipAddress?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.REFRESH_TOKEN,
            userId,
            ipAddress,
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log user creation
     */
    async logUserCreation(createdUser: SystemUser, createdBy: SystemUser, ipAddress?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.CREATE_USER,
            userId: createdBy.id,
            username: createdBy.username,
            ipAddress,
            details: {
                createdUserId: createdUser.id,
                createdUsername: createdUser.username,
                createdUserRole: createdUser.role,
            },
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log user status update
     */
    async logUserStatusUpdate(
        targetUserId: number,
        newStatus: boolean,
        updatedBy: SystemUser,
        ipAddress?: string
    ): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.UPDATE_USER_STATUS,
            userId: updatedBy.id,
            username: updatedBy.username,
            ipAddress,
            details: {
                targetUserId,
                newStatus,
            },
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log user role update
     */
    async logUserRoleUpdate(
        targetUserId: number,
        newRole: string,
        updatedBy: SystemUser,
        ipAddress?: string
    ): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.UPDATE_USER_ROLE,
            userId: updatedBy.id,
            username: updatedBy.username,
            ipAddress,
            details: {
                targetUserId,
                newRole,
            },
            timestamp: new Date(),
            success: true,
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Log rate limit exceeded
     */
    async logRateLimitExceeded(ipAddress: string, endpoint: string, userAgent?: string): Promise<void> {
        const entry: AuditLogEntry = {
            action: AuditAction.RATE_LIMIT_EXCEEDED,
            ipAddress,
            userAgent,
            details: { endpoint },
            timestamp: new Date(),
            success: false,
            errorMessage: 'Rate limit exceeded',
        };

        await this.writeAuditLog(entry);
    }

    /**
     * Write audit log entry
     * In production, this should write to a dedicated audit log table or external service
     */
    private async writeAuditLog(entry: AuditLogEntry): Promise<void> {
        try {
            // For now, we'll log to application logs
            // In production, consider writing to:
            // 1. Dedicated audit_logs table
            // 2. External logging service (ELK stack, Splunk, etc.)
            // 3. File-based audit logs

            const logMessage = this.formatAuditLogMessage(entry);
            
            if (entry.success) {
                this.logger.log(logMessage);
            } else {
                this.logger.warn(logMessage);
            }

            // TODO: Implement database storage for audit logs
            // await this.auditLogRepository.save(entry);

        } catch (error) {
            this.logger.error(`Failed to write audit log: ${error.message}`, error.stack);
        }
    }

    /**
     * Format audit log message for readability
     */
    private formatAuditLogMessage(entry: AuditLogEntry): string {
        const parts = [
            `[AUDIT]`,
            `Action: ${entry.action}`,
            entry.userId ? `UserId: ${entry.userId}` : null,
            entry.username ? `Username: ${entry.username}` : null,
            entry.ipAddress ? `IP: ${entry.ipAddress}` : null,
            `Success: ${entry.success}`,
            entry.errorMessage ? `Error: ${entry.errorMessage}` : null,
            entry.details ? `Details: ${JSON.stringify(entry.details)}` : null,
        ].filter(Boolean);

        return parts.join(' | ');
    }

    /**
     * Get audit statistics (for admin dashboard)
     */
    async getAuditStats(days: number = 7): Promise<any> {
        // This would query the audit logs table in production
        // For now, return mock data
        return {
            totalEvents: 0,
            loginAttempts: 0,
            failedLogins: 0,
            userCreations: 0,
            rateLimitExceeded: 0,
            period: `Last ${days} days`,
        };
    }
}
