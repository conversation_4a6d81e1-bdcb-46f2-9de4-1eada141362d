import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { Observable } from 'rxjs';

/**
 * JWT Authentication Guard
 * Validates access tokens and injects user context
 */
@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
    constructor(private reflector: Reflector) {
        super();
    }

    canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
        // Check if route is marked as public
        const isPublic = this.reflector.getAllAndOverride<boolean>('isPublic', [
            context.getHandler(),
            context.getClass(),
        ]);

        if (isPublic) {
            return true;
        }

        return super.canActivate(context);
    }

    handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
        // Handle authentication errors
        if (err || !user) {
            const request = context.switchToHttp().getRequest();
            const token = this.extractTokenFromHeader(request);
            
            if (!token) {
                throw new UnauthorizedException('Access token is required');
            }
            
            if (info?.name === 'TokenExpiredError') {
                throw new UnauthorizedException('Access token has expired');
            }
            
            if (info?.name === 'JsonWebTokenError') {
                throw new UnauthorizedException('Invalid access token');
            }
            
            throw new UnauthorizedException('Authentication failed');
        }

        return user;
    }

    /**
     * Extract token from Authorization header
     */
    private extractTokenFromHeader(request: any): string | undefined {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
}
