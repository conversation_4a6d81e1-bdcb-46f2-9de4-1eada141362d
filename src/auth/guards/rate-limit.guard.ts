import { Injectable, ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerException, ThrottlerLimitDetail } from '@nestjs/throttler';
import { Request } from 'express';

/**
 * Custom Rate Limiting Guard
 * Provides enhanced rate limiting with IP-based tracking
 */
@Injectable()
export class AuthRateLimitGuard extends ThrottlerGuard {

    /**
     * Generate throttler key based on IP and endpoint
     */
    protected generateKey(context: ExecutionContext, suffix: string): string {
        const request = context.switchToHttp().getRequest<Request>();
        const ip = this.getClientIP(request);
        const endpoint = request.route?.path || request.url;

        return `${ip}:${endpoint}:${suffix}`;
    }

    /**
     * Get client IP address with proxy support
     */
    private getClientIP(request: Request): string {
        return (
            request.headers['x-forwarded-for'] as string ||
            request.headers['x-real-ip'] as string ||
            request.connection.remoteAddress ||
            request.socket.remoteAddress ||
            'unknown'
        );
    }

    /**
     * Custom error message for rate limiting
     */
    protected async throwThrottlingException(
        context: ExecutionContext,
        throttlerLimitDetail: ThrottlerLimitDetail
    ): Promise<void> {
        const request = context.switchToHttp().getRequest<Request>();
        const ip = this.getClientIP(request);

        throw new ThrottlerException(
            `Too many requests from IP ${ip}. Please try again later.`
        );
    }
}
