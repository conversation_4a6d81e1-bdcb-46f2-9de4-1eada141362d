import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { SystemRole } from '../types/auth.types';
import { SystemUser } from '../entities/system-user.entity';

/**
 * Roles Guard
 * Checks if user has required role for accessing endpoint
 */
@Injectable()
export class RolesGuard implements CanActivate {
    constructor(private reflector: Reflector) {}

    canActivate(context: ExecutionContext): boolean {
        // Get required roles from decorator
        const requiredRoles = this.reflector.getAllAndOverride<SystemRole[]>('roles', [
            context.getHandler(),
            context.getClass(),
        ]);

        if (!requiredRoles) {
            return true; // No roles required
        }

        // Get user from request (injected by JWT guard)
        const request = context.switchToHttp().getRequest();
        const user: SystemUser = request.user;

        if (!user) {
            return false; // No user context
        }

        // Check if user has any of the required roles
        return this.hasRequiredRole(user, requiredRoles);
    }

    /**
     * Check if user has any of the required roles
     */
    private hasRequiredRole(user: SystemUser, requiredRoles: SystemRole[]): boolean {
        // Admin has access to everything
        if (user.role === SystemRole.ADMIN) {
            return true;
        }

        // Check if user's role is in required roles
        return requiredRoles.includes(user.role as SystemRole);
    }
}
