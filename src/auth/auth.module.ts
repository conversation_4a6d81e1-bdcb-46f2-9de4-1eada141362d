import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';

// Entities
import { SystemUser, RegisteredUser, RefreshToken } from './entities';

// Services
import { AuthService } from './services/auth.service';
import { UserService } from './services/user.service';
import { AdminSeederService } from './services/admin-seeder.service';
import { AuditLogService } from './services/audit-log.service';

// Strategies
import { JwtStrategy } from './strategies/jwt.strategy';

// Guards
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { RolesGuard } from './guards/roles.guard';

/**
 * Auth Module
 * Shared authentication logic and services
 */
@Module({
    imports: [
        // TypeORM entities
        TypeOrmModule.forFeature([
            SystemUser,
            RegisteredUser,
            RefreshToken,
        ]),

        // Passport configuration
        PassportModule.register({ defaultStrategy: 'jwt' }),

        // JWT configuration
        JwtModule.registerAsync({
            imports: [ConfigModule],
            useFactory: async (configService: ConfigService) => ({
                secret: configService.get<string>('JWT_SECRET', 'default-jwt-secret'),
                signOptions: {
                    expiresIn: configService.get<string>('JWT_ACCESS_EXPIRES_IN', '15m'),
                },
            }),
            inject: [ConfigService],
        }),

        // Throttler configuration
        ThrottlerModule.forRoot([
            {
                ttl: 60000, // 1 minute
                limit: 100, // 100 requests per minute (default)
            },
        ]),
    ],

    providers: [
        // Services
        AuthService,
        UserService,
        AdminSeederService,
        AuditLogService,

        // Strategies
        JwtStrategy,

        // Guards
        JwtAuthGuard,
        RolesGuard,
    ],

    exports: [
        // Services
        AuthService,
        UserService,

        // Guards
        JwtAuthGuard,
        RolesGuard,

        // TypeORM repositories (for other modules)
        TypeOrmModule,
    ],
})
export class AuthModule { }
