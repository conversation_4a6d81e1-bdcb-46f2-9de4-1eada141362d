import { Module } from '@nestjs/common';
import { FootballModule } from './football.module';
import { FixtureController } from './controllers/fixture.controller';
import { LeagueController } from './controllers/league.controller';
import { TeamController } from './controllers/team.controller';

// Football API Module - Contains controllers for HTTP endpoints
@Module({
  imports: [FootballModule],
  controllers: [
    FixtureController,
    LeagueController,
    TeamController,
  ],
})
export class FootballApiModule { }
