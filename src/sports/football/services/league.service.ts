import { Injectable, Logger, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import axios from 'axios';
import { League } from '../models/league.entity';
import { CreateLeagueDto, UpdateLeagueDto, GetLeaguesDto, LeagueResponseDto, PaginatedLeaguesResponse } from '../models/league.dto';
import { CacheService } from '../../../core';
import { ImageService } from '../../../shared';

@Injectable()
export class LeagueService {
    private readonly logger = new Logger(LeagueService.name);

    constructor(
        @InjectRepository(League)
        private readonly leagueRepository: Repository<League>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly imageService: ImageService,
    ) { }

    /**
 * Get league(s) by external ID, optionally filtered by season
 * @param externalId - External ID of the league
 * @param season - Optional season to filter
 * @returns League details or list of leagues
 */
    async getLeagueById(externalId: number, season?: number): Promise<LeagueResponseDto | PaginatedLeaguesResponse> {
        const cacheKey = `league_detail_${externalId}${season ? `_${season}` : ''}`;
        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning league from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        const qb = this.leagueRepository
            .createQueryBuilder('league')
            .where('league.externalId = :externalId', { externalId });

        if (season) {
            qb.andWhere('league.season = :season', { season });
        } else {
            qb.orderBy('league.season', 'DESC');
        }

        let leagues = season
            ? await qb.getOne()
            : await qb.getMany();

        if (!leagues || (Array.isArray(leagues) && leagues.length === 0)) {
            this.logger.debug(`League ${externalId}${season ? ` for season ${season}` : ''} not found in DB, fetching from API`);
            const apiQuery: any = { id: externalId };
            if (season) {
                apiQuery.season = season;
            }
            const fetchedLeagues = await this.fetchFromApi(apiQuery);
            if (fetchedLeagues.length === 0) {
                throw new NotFoundException(`League with externalId ${externalId}${season ? ` and season ${season}` : ''} not found`);
            }
            try {
                await this.leagueRepository.save(fetchedLeagues);
                this.logger.debug(`Saved ${fetchedLeagues.length} leagues to DB`);
            } catch (error) {
                this.logger.error(`Failed to save leagues to DB: ${error.message}`);
            }

            const refreshedLeagues = season
                ? await this.leagueRepository.findOne({ where: { externalId, season } })
                : await this.leagueRepository.find({ where: { externalId }, order: { season: 'DESC' } });

            if (!refreshedLeagues || (Array.isArray(refreshedLeagues) && refreshedLeagues.length === 0)) {
                throw new NotFoundException(`League with externalId ${externalId}${season ? ` and season ${season}` : ''} not found after API fetch`);
            }
            leagues = refreshedLeagues;
        }

        let response;
        if (season || !Array.isArray(leagues)) {
            response = this.mapToResponseDto([leagues as League])[0];
        } else {
            response = {
                data: this.mapToResponseDto(leagues),
                meta: {
                    totalItems: leagues.length,
                    totalPages: 1,
                    currentPage: 1,
                    limit: leagues.length,
                },
                status: 200,
            } as PaginatedLeaguesResponse;
        }

        await this.cacheService.setCache(cacheKey, JSON.stringify(response), 604800);
        this.logger.debug(`Cached league response for key: ${cacheKey}`);
        return response;
    }


    /**
     * Create a new league manually
     * @param createLeagueDto - League data
     * @returns Created league
     */
    async createLeague(createLeagueDto: CreateLeagueDto): Promise<LeagueResponseDto> {
        // Validate logo/flag URLs if provided
        if (createLeagueDto.logo && !this.isValidUrl(createLeagueDto.logo)) {
            throw new BadRequestException('Invalid logo URL');
        }
        if (createLeagueDto.flag && !this.isValidUrl(createLeagueDto.flag)) {
            throw new BadRequestException('Invalid flag URL');
        }

        const existingLeague = await this.leagueRepository.findOneBy({
            externalId: createLeagueDto.externalId,
            season: createLeagueDto.season,
        });
        if (existingLeague) {
            throw new ConflictException(`League with externalId ${createLeagueDto.externalId} and season ${createLeagueDto.season} already exists`);
        }

        const league = this.leagueRepository.create({
            externalId: createLeagueDto.externalId,
            name: createLeagueDto.name,
            country: createLeagueDto.country.toLowerCase(),
            logo: createLeagueDto.logo,
            flag: createLeagueDto.flag,
            season: createLeagueDto.season,
            active: createLeagueDto.active !== undefined ? createLeagueDto.active : true,
            season_detail: createLeagueDto.season_detail,
            type: createLeagueDto.type.toLocaleLowerCase() || 'unknown',
        });

        try {
            const savedLeague = await this.leagueRepository.save(league);
            this.logger.debug(`Created league with id ${savedLeague.id}`);

            // Clear specific cache
            await this.cacheService.deleteByPattern(`leagues_list_*_${savedLeague.season}_${savedLeague.country}_*`);
            this.logger.debug('Cleared specific league cache after creation');

            return {
                id: savedLeague.id,
                externalId: savedLeague.externalId,
                name: savedLeague.name,
                country: savedLeague.country,
                logo: savedLeague.logo,
                flag: savedLeague.flag,
                season: savedLeague.season,
                active: savedLeague.active,
                type: savedLeague.type.toLowerCase() || 'unknown',
            };
        } catch (error) {
            this.logger.error(`Failed to create league: ${error.message}`);
            if (error.code === '23505') {
                throw new ConflictException('Duplicate league entry');
            }
            throw new BadRequestException(`Failed to create league: ${error.message}`);
        }
    }

    /**
     * Update an existing league
     * @param id - League ID
     * @param updateLeagueDto - League data to update
     * @returns Updated league
     */
    async updateLeague(id: number, updateLeagueDto: UpdateLeagueDto): Promise<LeagueResponseDto> {
        // Check if DTO is empty
        if (Object.keys(updateLeagueDto).length === 0) {
            throw new BadRequestException('No fields provided for update');
        }

        // Validate logo/flag URLs if provided
        if (updateLeagueDto.logo && !this.isValidUrl(updateLeagueDto.logo)) {
            throw new BadRequestException('Invalid logo URL');
        }
        if (updateLeagueDto.flag && !this.isValidUrl(updateLeagueDto.flag)) {
            throw new BadRequestException('Invalid flag URL');
        }

        const league = await this.leagueRepository.findOneBy({ id });
        if (!league) {
            throw new NotFoundException(`League with id ${id} not found`);
        }

        if (updateLeagueDto.name) {
            league.name = updateLeagueDto.name;
        }
        if (updateLeagueDto.country) {
            league.country = updateLeagueDto.country.toLowerCase();
        }
        if (updateLeagueDto.logo) {
            league.logo = updateLeagueDto.logo;
        }
        if (updateLeagueDto.flag) {
            league.flag = updateLeagueDto.flag;
        }
        if (updateLeagueDto.active !== undefined) {
            league.active = updateLeagueDto.active;
        }
        if (updateLeagueDto.season_detail) {
            league.season_detail = updateLeagueDto.season_detail;
        }
        if (updateLeagueDto.type) {
            league.type = updateLeagueDto.type;
        }
        try {
            const savedLeague = await this.leagueRepository.save(league);
            this.logger.debug(`Updated league with id ${savedLeague.id}`);

            // Clear specific cache
            await this.cacheService.deleteByPattern(`leagues_list_*_${savedLeague.season}_${savedLeague.country}_*`);
            this.logger.debug('Cleared specific league cache after update');

            return {
                id: savedLeague.id,
                externalId: savedLeague.externalId,
                name: savedLeague.name,
                country: savedLeague.country,
                logo: savedLeague.logo,
                flag: savedLeague.flag,
                season: savedLeague.season,
                active: savedLeague.active,
                type: savedLeague.type.toLowerCase() || 'unknown',
            };
        } catch (error) {
            this.logger.error(`Failed to update league: ${error.message}`);
            throw new BadRequestException(`Failed to update league: ${error.message}`);
        }
    }

    /**
     * Validate URL format
     * @param url - URL to validate
     * @returns True if valid, false otherwise
     */
    private isValidUrl(url: string): boolean {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Get leagues by query parameters
     * @param query - Query parameters (season, country, active, page, limit, newdb)
     * @returns Paginated list of leagues
     */
    async getLeagues(query: GetLeaguesDto): Promise<PaginatedLeaguesResponse> {
        query.last = 3
        const page = query.page || 1;
        const limit = query.limit || 10;

        const activeKey = query.active !== undefined ? query.active : 'all';

        const typeKey = query.type ?? '';
        const teamKey = query.team ?? '';
        const leagueKey = query.league ?? '';
        const cacheKey = `leagues_list_${query.season ?? ''}_${query.country ?? ''}_${activeKey}_${typeKey}_${teamKey}_${leagueKey}_${page}_${limit}`;

        if (query.newdb === true) {
            this.logger.debug(`newdb=true, fetching directly from API for query: ${JSON.stringify(query)}`);
            const leagues = await this.fetchFromApi(query);
            let paginatedResult = { leagues, totalItems: leagues.length };

            if (leagues.length > 0) {
                try {
                    await this.leagueRepository.save(leagues);
                    this.logger.debug(`Saved ${leagues.length} leagues to DB`);
                    paginatedResult = await this.fetchFromDb(query);
                } catch (error) {
                    this.logger.error(`Failed to save leagues to DB: ${error.message}`);
                }
            }

            await this.cacheService.deleteByPattern(`leagues_list_*_${query.season ?? ''}_${query.country ? query.country.toLowerCase() : ''}_*`);
            this.logger.debug('Cleared specific league cache after API fetch');

            const response: PaginatedLeaguesResponse = {
                data: this.mapToResponseDto(paginatedResult.leagues),
                meta: {
                    totalItems: paginatedResult.totalItems,
                    totalPages: Math.ceil(paginatedResult.totalItems / limit),
                    currentPage: page,
                    limit,
                },
                status: 200,
            };

            if (response.data.length > 0) {
                await this.cacheService.setCache(cacheKey, JSON.stringify(response), 604800);
                this.logger.debug(`Cached paginated response for key: ${cacheKey}`);
            }
            return response;
        }

        const cached = await this.cacheService.getCache(cacheKey);
        if (cached) {
            this.logger.debug(`Returning leagues from cache for key: ${cacheKey}`);
            return JSON.parse(cached);
        }

        let { leagues, totalItems } = await this.fetchFromDb(query);
        if (leagues.length === 0) {
            this.logger.debug(`No leagues found in DB for query: ${JSON.stringify(query)}, fetching from API`);
            leagues = await this.fetchFromApi(query);
            if (leagues.length > 0) {
                try {
                    await this.leagueRepository.save(leagues);
                    this.logger.debug(`Saved ${leagues.length} leagues to DB`);
                    const paginatedResult = await this.fetchFromDb(query);
                    leagues = paginatedResult.leagues;
                    totalItems = paginatedResult.totalItems;
                } catch (error) {
                    this.logger.error(`Failed to save leagues to DB: ${error.message}`);
                }
            }
        }

        const response: PaginatedLeaguesResponse = {
            data: this.mapToResponseDto(leagues),
            meta: {
                totalItems,
                totalPages: Math.ceil(totalItems / limit),
                currentPage: page,
                limit,
            },
            status: 200,
        };

        if (response.data.length > 0) {
            await this.cacheService.setCache(cacheKey, JSON.stringify(response), 604800);
            this.logger.debug(`Cached paginated response for key: ${cacheKey}`);
        }
        return response;
    }

    private async fetchFromDb(query: GetLeaguesDto): Promise<{ leagues: League[]; totalItems: number }> {
        const page = query.page || 1;
        const limit = query.limit || 10;
        const skip = (page - 1) * limit;

        const qb = this.leagueRepository.createQueryBuilder('league');
        if (query.season) {
            qb.andWhere('league.season = :season', { season: query.season });
        }
        if (query.season) {
            qb.andWhere('league.season = :season', { season: query.season });
        }
        if (query.country) {
            qb.andWhere('league.country = :country', { country: query.country.toLowerCase() });
        }
        if (query.active !== undefined) {
            qb.andWhere('league.active = :active', { active: query.active });
        }
        if (query.type) {
            qb.andWhere('league.type = :type', { type: query.type });
        }
        if (query.league) {
            qb.andWhere('league.externalId = :externalId', { externalId: query.league });
        }
        /*
        if (query.team) {
            // Join với bảng fixtures để kiểm tra team tham gia giải đấu
            qb.innerJoin('fixtures', 'fixture', 'fixture.leagueId = league.externalId')
                .andWhere('(fixture.homeTeamId = :team OR fixture.awayTeamId = :team)', { team: query.team });
        }
        */
        const [leagues, totalItems] = await qb
            .skip(skip)
            .take(limit)
            .getManyAndCount();

        this.logger.debug(`Fetched ${leagues.length} leagues from DB for query: ${JSON.stringify(query)}`);
        return { leagues, totalItems };
    }

    private async fetchFromApi(query: GetLeaguesDto | { id: number }): Promise<League[]> {
        try {
            const apiQuery: any = {};
            if ('id' in query) {
                apiQuery.id = query.id;
            } else {
                const { page, limit, newdb, type, league, active, ...rest } = query as GetLeaguesDto;
                Object.assign(apiQuery, rest);

                if (league) {
                    apiQuery.id = league;
                }

                if (apiQuery.country) {
                    apiQuery.country = apiQuery.country.charAt(0).toUpperCase() + apiQuery.country.slice(1).toLowerCase();
                }
            }
            const response = await this.executeWithRetry(async () => {
                const apiUrl = `${this.configService.get('app.apiFootballUrl')}/leagues`;
                const headers = { 'x-apisports-key': this.configService.get('app.apiFootballKey') };
                this.logger.debug(`Calling API: ${apiUrl} with params: ${JSON.stringify(apiQuery)}`);
                return axios.get(apiUrl, {
                    params: apiQuery,
                    headers,
                });
            });

            this.logger.debug(`API response for query ${JSON.stringify(apiQuery)}`);
            // this.logger.debug(`API response for query ${JSON.stringify(apiQuery)}: ${JSON.stringify(response.data)}`);

            if (!response.data.response || response.data.response.length === 0) {
                this.logger.warn(`No data returned from API for query ${JSON.stringify(apiQuery)}`);
                return [];
            }

            const leagues = await Promise.all(
                response.data.response.flatMap(async (apiData: any) => {
                    if (!apiData.league || !apiData.league.id) {
                        this.logger.warn(`Invalid league data for league ${apiData.league?.id || 'unknown'}`);
                        return [];
                    }

                    // Process all seasons for this league
                    const seasons = apiData.seasons || [];
                    if (seasons.length === 0) {
                        this.logger.warn(`No seasons found for league ${apiData.league.id}`);
                        return [];
                    }

                    const leaguePromises = seasons.map(async (seasonData: any) => {
                        let league = await this.leagueRepository.findOneBy({
                            externalId: apiData.league.id,
                            season: seasonData.year || 0,
                        });

                        if (!league) {
                            // Download images only once per league (not per season)
                            const logoPath = apiData.league.logo
                                ? await this.imageService.downloadImage(apiData.league.logo, 'leagues', `${apiData.league.id}.png`)
                                : '';
                            const flagPath = apiData.country?.flag
                                ? await this.imageService.downloadImage(apiData.country.flag, 'flags', `${apiData.country.code || apiData.league.id}.svg`)
                                : '';

                            league = this.leagueRepository.create({
                                externalId: apiData.league.id,
                                name: apiData.league.name || 'Unknown',
                                country: (apiData.country?.name || 'Unknown').toLowerCase(),
                                logo: logoPath,
                                flag: flagPath,
                                season: seasonData.year || 0,
                                active: seasonData.current || false,
                                type: apiData.league.type?.toLowerCase() || 'unknown',
                                season_detail: {
                                    year: seasonData.year,
                                    start: seasonData.start,
                                    end: seasonData.end,
                                    current: seasonData.current,
                                    coverage: seasonData.coverage,
                                },
                            });

                            try {
                                this.logger.debug(`Saving league: externalId=${apiData.league.id}, season=${seasonData.year}`);
                                await this.leagueRepository.save(league);
                            } catch (error) {
                                if (error.code === '23505') {
                                    this.logger.debug(`League already exists, fetching existing: externalId=${apiData.league.id}, season=${seasonData.year}`);
                                    league = await this.leagueRepository.findOneBy({
                                        externalId: apiData.league.id,
                                        season: seasonData.year,
                                    });
                                    if (!league) {
                                        this.logger.error(`Failed to fetch existing league after duplicate error`);
                                        return null;
                                    }
                                } else {
                                    this.logger.error(`Failed to save league: ${error.message}`);
                                    return null;
                                }
                            }
                        }
                        return league;
                    });

                    return Promise.all(leaguePromises);
                })
            ).then(results => results.flat());

            const validLeagues = leagues.filter((league): league is League => league !== null);
            this.logger.debug(`Processed ${validLeagues.length} valid leagues from API`);
            return validLeagues;
        } catch (error) {
            this.logger.error(`Failed to fetch from API: ${error.message}`);
            return [] as League[];
        }
    }

    private async executeWithRetry<T>(fn: () => Promise<T>, retries = 3, delay = 1000): Promise<T> {
        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                return await fn();
            } catch (error) {
                if (attempt === retries) {
                    this.logger.error(`Failed after ${retries} attempts: ${error.message}`);
                    throw error;
                }
                this.logger.warn(`Attempt ${attempt} failed: ${error.message}. Retrying after ${delay}ms...`);
                await new Promise((resolve) => setTimeout(resolve, delay));
            }
        }
        throw new Error('Unexpected error in retry logic');
    }

    private mapToResponseDto(leagues: League[]): LeagueResponseDto[] {
        return leagues.map(league => ({
            id: league.id,
            externalId: league.externalId,
            name: league.name,
            country: league.country,
            logo: league.logo,
            flag: league.flag,
            season: league.season,
            active: league.active,
            season_detail: league.season_detail,
            type: league.type.toLowerCase() || 'unknown',
        }));
    }
}