import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, Unique, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';

interface SeasonDetail {
  year: number;
  start: string;
  end: string;
  current: boolean;
  coverage: {
    fixtures: boolean;
    standings: boolean;
    players: boolean;
    top_scorers: boolean;
    top_assists: boolean;
    top_cards: boolean;
    injuries: boolean;
    predictions: boolean;
    odds: boolean;
  };
}

@Entity('leagues')
@Unique(['externalId', 'season'])
export class League {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  externalId: number;

  @Column()
  @Index('idx_league_name')
  name: string;

  @Column()
  @Index('idx_league_country')
  country: string;

  @Column({ nullable: true })
  logo: string;

  @Column({ nullable: true })
  flag: string;

  @Column()
  @Index('idx_league_season')
  season: number;

  @Column({ type: 'jsonb', nullable: true })
  season_detail: SeasonDetail;

  @Column({ type: 'boolean', default: true })
  @Index('idx_league_active')
  active: boolean;

  @Column()
  @Index('idx_league_type')
  type: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}