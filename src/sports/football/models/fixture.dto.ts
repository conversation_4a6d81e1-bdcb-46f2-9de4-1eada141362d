import { IsInt, IsString, IsOptional, IsEnum, IsDateString, IsObject, Min, Max, IsNotEmpty, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

export enum FixtureStatus {
  Upcoming = 'UPCOMING',
  NotStarted = 'NS',
  FirstHalf = '1H',
  Halftime = 'HT',
  SecondHalf = '2H',
  ExtraTime = 'ET',
  Penalty = 'P',
  FullTime = 'FT',
  Cancelled = 'CANC',
  Live = 'LIVE',
}

/** DTO for querying fixtures */
export class GetFixturesDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  id?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  league?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  season?: number;

  @IsDateString()
  @IsOptional()
  date?: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  team?: number;

  @IsEnum(FixtureStatus, { each: true })
  @IsOptional()
  status?: FixtureStatus[];

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @IsOptional()
  page: number = 1; // Mặc định page = 1

  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  @IsOptional()
  limit: number = 10; // Mặc định limit = 10, tối đa 100

  @Type(() => Boolean)
  @IsBoolean()
  @IsOptional()
  newdb?: boolean;

  @Type(() => Boolean)
  @IsBoolean()
  @IsOptional()
  isHot?: boolean;

}

/** DTO for fixture response */
export class FixtureResponseDto {
  @IsInt()
  id: number;

  @IsInt()
  externalId: number;

  @IsInt()
  leagueId: number;

  @IsString()
  leagueName: string;

  @IsInt()
  season: number;

  @IsString()
  round: string;

  @IsInt()
  homeTeamId: number;

  @IsString()
  homeTeamName: string;

  @IsString()
  homeTeamLogo: string;

  @IsInt()
  awayTeamId: number;

  @IsString()
  awayTeamName: string;

  @IsString()
  awayTeamLogo: string;

  @IsString()
  slug: string;

  @IsDateString()
  date: string;

  @IsObject()
  venue: {
    id?: number;
    name?: string;
    city?: string;
  };

  @IsString()
  @IsOptional()
  referee?: string;

  @IsString()
  status: string;

  @IsString()
  statusLong: string;

  @IsInt()
  statusExtra: number;

  @IsInt()
  @IsOptional()
  elapsed?: number;

  @IsInt()
  @IsOptional()
  goalsHome?: number;

  @IsInt()
  @IsOptional()
  goalsAway?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  scoreHalftimeHome?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  scoreHalftimeAway?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  scoreFulltimeHome?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  scoreFulltimeAway?: number;

  @IsObject()
  @IsOptional()
  periods?: {
    first?: number;
    second?: number;
  };

  @IsInt()
  timestamp: number;

  @IsBoolean()
  isHot: boolean;

}

/** Paginated response for fixtures */
export interface PaginatedFixturesResponse {
  data: FixtureResponseDto[];
  meta: {
    totalItems: number;
    totalPages: number;
    currentPage: number;
    limit: number;
  };
  status: number;
}

/** DTO for creating a fixture manually */
export class CreateFixtureDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  externalId?: number;

  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  leagueId: number;

  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  homeTeamId: number;

  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  awayTeamId: number;

  @IsDateString()
  @IsNotEmpty()
  date: string;

  @Type(() => Number)
  @IsInt()
  @IsNotEmpty()
  season: number;

  @IsString()
  @IsOptional()
  round?: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  venueId?: number;

  @IsString()
  @IsOptional()
  venueName?: string;

  @IsString()
  @IsOptional()
  venueCity?: string;

  @IsString()
  @IsOptional()
  referee?: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  timestamp?: number;

  @IsObject()
  @IsNotEmpty()
  data: {
    homeTeamName: string;
    homeTeamLogo?: string;
    awayTeamName: string;
    awayTeamLogo?: string;
    status: string;
    statusLong: string;
    statusExtra: number;
    elapsed?: number;
    goalsHome?: number;
    goalsAway?: number;
    scoreHalftimeHome?: number;
    scoreHalftimeAway?: number;
    scoreFulltimeHome?: number;
    scoreFulltimeAway?: number;
    periods?: {
      first?: number;
      second?: number;
    };
  };

  @Type(() => Boolean)
  @IsBoolean()
  @IsOptional()
  isHot?: boolean;
}

/** DTO for updating a fixture */
export class UpdateFixtureDto {
  @Type(() => Number)
  @IsInt()
  @IsOptional()
  leagueId?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  homeTeamId?: number;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  awayTeamId?: number;

  @IsDateString()
  @IsOptional()
  date?: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  season?: number;

  @IsString()
  @IsOptional()
  round?: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  venueId?: number;

  @IsString()
  @IsOptional()
  venueName?: string;

  @IsString()
  @IsOptional()
  venueCity?: string;

  @IsString()
  @IsOptional()
  referee?: string;

  @Type(() => Number)
  @IsInt()
  @IsOptional()
  timestamp?: number;


  @IsObject()
  @IsOptional()
  data?: Partial<{
    homeTeamName: string;
    homeTeamLogo: string;
    awayTeamName: string;
    awayTeamLogo: string;
    status: string;
    statusLong: string;
    statusExtra: number;
    elapsed: number;
    goalsHome: number;
    goalsAway: number;
    scoreHalftimeHome: number;
    scoreHalftimeAway: number;
    scoreFulltimeHome: number;
    scoreFulltimeAway: number;
    periods: {
      first?: number;
      second?: number;
    };
  }>;

  @Type(() => Boolean)
  @IsBoolean()
  @IsOptional()
  isHot?: boolean;
}