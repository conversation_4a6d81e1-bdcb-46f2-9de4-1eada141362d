import { Entity, Column, PrimaryGeneratedColumn, Index } from 'typeorm';

interface FixtureData {
  homeTeamName: string;
  homeTeamLogo: string;
  awayTeamName: string;
  awayTeamLogo: string;
  status: string;
  statusLong: string;
  statusExtra: number;
  elapsed?: number;
  goalsHome?: number;
  goalsAway?: number;
  scoreHalftimeHome?: number;
  scoreHalftimeAway?: number;
  scoreFulltimeHome?: number;
  scoreFulltimeAway?: number;
  periods?: {
    first?: number;
    second?: number;
  };
  events?: {
    time: { elapsed: number; extra?: number };
    team: { id: number; name: string; logo: string };
    player: { id: number; name: string };
    assist?: { id?: number; name?: string };
    type: string;
    detail: string;
  }[];
  lineups?: {
    team: { id: number; name: string; logo: string };
    formation: string;
    players: {
      id: number;
      name: string;
      number: number;
      position: string;
    }[];
  }[];
  score?: {
    halftime: { home: number; away: number };
    fulltime: { home: number; away: number };
    extratime?: { home: number; away: number };
    penalty?: { home: number; away: number };
  };
}

@Entity('fixtures')
export class Fixture {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true, unique: true })
  externalId: number;

  @Index()
  @Column({ type: 'boolean', default: false })
  isHot: boolean;

  @Column()
  leagueId: number;

  @Column()
  leagueName: string;

  @Column()
  season: number;

  @Column()
  round: string;

  @Column()
  homeTeamId: number;

  @Column()
  awayTeamId: number;

  @Column({ unique: true })
  slug: string;

  @Index()
  @Column({ type: 'timestamp with time zone' })
  date: Date;

  @Column({ nullable: true })
  venueId: number;

  @Column({ nullable: true })
  venueName: string;

  @Column({ nullable: true })
  venueCity: string;

  @Column({ nullable: true })
  referee: string;

  @Column()
  source: 'api' | 'manual';

  @Column('integer', { nullable: true })
  createdBy: number | null;

  @Column({ type: 'jsonb' })
  data: FixtureData;

  @Column({ type: 'bigint' })
  timestamp: number;

  @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ type: 'timestamp with time zone', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}