import { Controller, Get, Query, Param } from '@nestjs/common';
import { TeamService } from '../services/team.service';
import { GetTeamsDto, PaginatedTeamsResponse, TeamResponseDto } from '../models/team.dto';
import { TeamStatisticsService } from '../services/team-statistics.service';
import { GetTeamStatisticsDto, TeamStatisticsResponseDto } from '../models/team-statistics.dto';

@Controller('football/teams')
export class TeamController {
    constructor(
        private readonly teamService: TeamService,
        private readonly teamStatisticsService: TeamStatisticsService
    ) { }


    /**
     * Get teams with pagination and filters
     * @param query - Query parameters (league, season, country, page, limit)
     * @returns Paginated list of teams
     */
    @Get()
    async getTeams(@Query() query: GetTeamsDto): Promise<PaginatedTeamsResponse> {
        return this.teamService.getTeams(query);
    }

    /**
     * Get team statistics by league, season, and team
     * @param query - Query parameters (league, season, team)
     * @returns Team statistics
     */
    @Get('statistics')
    async getTeamStatistics(
        @Query() query: GetTeamStatisticsDto,
    ): Promise<{ data: TeamStatisticsResponseDto; status: number }> {
        return this.teamStatisticsService.getTeamStatistics(query);
    }

    @Get(':externalId')
    async getTeamById(@Param('externalId') externalId: number): Promise<{ data: TeamResponseDto; status: number }> {
        const team = await this.teamService.getTeamById(externalId);
        return { data: team, status: 200 };
    }
}