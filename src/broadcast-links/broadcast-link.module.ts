import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BroadcastLinkService } from './broadcast-link.service';
import { BroadcastLinkController } from './broadcast-link.controller';
import { BroadcastLink } from './broadcast-link.entity';
import { Fixture } from '../sports/football/models/fixture.entity';

@Module({
    imports: [
        TypeOrmModule.forFeature([BroadcastLink, Fixture]),
    ],
    controllers: [BroadcastLinkController],
    providers: [BroadcastLinkService],
    exports: [BroadcastLinkService],
})
export class BroadcastLinkModule { }