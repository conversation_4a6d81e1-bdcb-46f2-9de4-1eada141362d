import { Controller, Post, Get, Body, Param, Patch, Delete } from '@nestjs/common';
import { BroadcastLinkService } from './broadcast-link.service';
import { CreateBroadcastLinkDto, BroadcastLinkResponseDto, UpdateBroadcastLinkDto } from './broadcast-link.dto';

@Controller('broadcast-links')
export class BroadcastLinkController {
    constructor(private readonly broadcastLinkService: BroadcastLinkService) { }

    /**
     * Create a new broadcast link
     * @param createBroadcastLinkDto - Broadcast link data
     * @returns Created broadcast link
     */
    @Post()
    async createBroadcastLink(
        @Body() createBroadcastLinkDto: CreateBroadcastLinkDto,
    ): Promise<{ data: BroadcastLinkResponseDto; status: number }> {
        const broadcastLink = await this.broadcastLinkService.createBroadcastLink(createBroadcastLinkDto);
        return { data: broadcastLink, status: 201 };
    }

    /**
     * Get broadcast links for a fixture
     * @param fixtureId - Fixture external ID
     * @returns List of broadcast links
     */
    @Get('fixture/:fixtureId')
    async getBroadcastLinksByFixtureId(
        @Param('fixtureId') fixtureId: number,
    ): Promise<{ data: BroadcastLinkResponseDto[]; status: number }> {
        const broadcastLinks = await this.broadcastLinkService.getBroadcastLinksByFixtureId(fixtureId);
        return { data: broadcastLinks, status: 200 };
    }

    /**
   * Update a broadcast link
   * @param id - Broadcast link ID
   * @param updateBroadcastLinkDto - Broadcast link data to update
   * @returns Updated broadcast link
   */
    @Patch(':id')
    async updateBroadcastLink(
        @Param('id') id: number,
        @Body() updateBroadcastLinkDto: UpdateBroadcastLinkDto,
    ): Promise<{ data: BroadcastLinkResponseDto; status: number }> {
        const broadcastLink = await this.broadcastLinkService.updateBroadcastLink(id, updateBroadcastLinkDto);
        return { data: broadcastLink, status: 200 };
    }

    /**
     * Delete a broadcast link
     * @param id - Broadcast link ID
     * @returns No content
     */
    @Delete(':id')
    async deleteBroadcastLink(@Param('id') id: number): Promise<{ status: number }> {
        await this.broadcastLinkService.deleteBroadcastLink(id);
        return { status: 204 };
    }
}