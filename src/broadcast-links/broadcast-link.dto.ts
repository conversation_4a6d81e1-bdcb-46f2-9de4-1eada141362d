import { IsInt, IsString, IsNotEmpty, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class CreateBroadcastLinkDto {
    @Type(() => Number)
    @IsInt()
    @IsNotEmpty()
    fixtureId: number;

    @IsString()
    @IsNotEmpty()
    linkName: string;

    @IsString()
    @IsNotEmpty()
    linkUrl: string;

    @IsString()
    @IsNotEmpty()
    linkComment: string;

    @Type(() => Number)
    @IsInt()
    @IsNotEmpty()
    addedBy: number;
}
export class UpdateBroadcastLinkDto {
    @IsString()
    @IsOptional()
    linkName?: string;

    @IsString()
    @IsOptional()
    linkUrl?: string;

    @IsString()
    @IsNotEmpty()
    linkComment?: string;

    @Type(() => Number)
    @IsInt()
    @IsOptional()
    addedBy?: number;
}

export interface BroadcastLinkResponseDto {
    id: number;
    fixtureId: number;
    linkName: string;
    linkComment: string;
    linkUrl: string;
    addedBy: number;
    createdAt: string;
    updatedAt: string;
}