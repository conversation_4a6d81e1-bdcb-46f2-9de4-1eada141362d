import { Injectable, NotFoundException, Logger, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BroadcastLink } from './broadcast-link.entity';
import { Fixture } from '../sports/football/models/fixture.entity';
import { CreateBroadcastLinkDto, BroadcastLinkResponseDto, UpdateBroadcastLinkDto } from './broadcast-link.dto';
import { isURL } from 'class-validator';

@Injectable()
export class BroadcastLinkService {
    private readonly logger = new Logger(BroadcastLinkService.name);

    constructor(
        @InjectRepository(BroadcastLink)
        private readonly broadcastLinkRepository: Repository<BroadcastLink>,
        @InjectRepository(Fixture)
        private readonly fixtureRepository: Repository<Fixture>,
    ) { }

    /**
     * Create a new broadcast link
     * @param createBroadcastLinkDto - Broadcast link data
     * @returns Created broadcast link
     */
    async createBroadcastLink(createBroadcastLinkDto: CreateBroadcastLinkDto): Promise<BroadcastLinkResponseDto> {
        // Validate fixtureId
        const fixture = await this.fixtureRepository.findOneBy({ externalId: createBroadcastLinkDto.fixtureId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${createBroadcastLinkDto.fixtureId} not found`);
        }

        // Validate linkUrl
        if (!isURL(createBroadcastLinkDto.linkUrl)) {
            throw new BadRequestException(`Invalid URL: ${createBroadcastLinkDto.linkUrl}`);
        }

        // Create broadcast link
        const broadcastLink = new BroadcastLink();
        broadcastLink.fixtureId = createBroadcastLinkDto.fixtureId;
        broadcastLink.linkName = createBroadcastLinkDto.linkName;
        broadcastLink.linkUrl = createBroadcastLinkDto.linkUrl;
        broadcastLink.addedBy = createBroadcastLinkDto.addedBy;
        broadcastLink.linkComment = createBroadcastLinkDto.linkComment;

        try {
            const savedBroadcastLink = await this.broadcastLinkRepository.save(broadcastLink);
            this.logger.debug(`Created broadcast link with id ${savedBroadcastLink.id} for fixture ${createBroadcastLinkDto.fixtureId}`);
            return this.mapToResponseDto(savedBroadcastLink);
        } catch (error) {
            this.logger.error(`Failed to create broadcast link: ${error.message}`);
            throw new BadRequestException(`Failed to create broadcast link: ${error.message}`);
        }
    }

    /**
 * Update an existing broadcast link
 * @param id - Broadcast link ID
 * @param updateBroadcastLinkDto - Broadcast link data to update
 * @returns Updated broadcast link
 */
    async updateBroadcastLink(id: number, updateBroadcastLinkDto: UpdateBroadcastLinkDto): Promise<BroadcastLinkResponseDto> {
        // Find existing broadcast link
        const broadcastLink = await this.broadcastLinkRepository.findOneBy({ id });
        if (!broadcastLink) {
            throw new NotFoundException(`Broadcast link with id ${id} not found`);
        }

        // Validate linkUrl if provided
        if (updateBroadcastLinkDto.linkUrl && !isURL(updateBroadcastLinkDto.linkUrl)) {
            throw new BadRequestException(`Invalid URL: ${updateBroadcastLinkDto.linkUrl}`);
        }

        // Update fields if provided
        if (updateBroadcastLinkDto.linkName) {
            broadcastLink.linkName = updateBroadcastLinkDto.linkName;
        }
        if (updateBroadcastLinkDto.linkUrl) {
            broadcastLink.linkUrl = updateBroadcastLinkDto.linkUrl;
        }
        if (updateBroadcastLinkDto.addedBy) {
            broadcastLink.addedBy = updateBroadcastLinkDto.addedBy;
        }

        try {
            const savedBroadcastLink = await this.broadcastLinkRepository.save(broadcastLink);
            this.logger.debug(`Updated broadcast link with id ${savedBroadcastLink.id}`);
            return this.mapToResponseDto(savedBroadcastLink);
        } catch (error) {
            this.logger.error(`Failed to update broadcast link: ${error.message}`);
            throw new BadRequestException(`Failed to update broadcast link: ${error.message}`);
        }
    }

    /**
     * Delete a broadcast link
     * @param id - Broadcast link ID
     */
    async deleteBroadcastLink(id: number): Promise<void> {
        // Find existing broadcast link
        const broadcastLink = await this.broadcastLinkRepository.findOneBy({ id });
        if (!broadcastLink) {
            throw new NotFoundException(`Broadcast link with id ${id} not found`);
        }

        try {
            await this.broadcastLinkRepository.delete(id);
            this.logger.debug(`Deleted broadcast link with id ${id}`);
        } catch (error) {
            this.logger.error(`Failed to delete broadcast link: ${error.message}`);
            throw new BadRequestException(`Failed to delete broadcast link: ${error.message}`);
        }
    }
    /**
     * Get broadcast links for a fixture
     * @param fixtureId - Fixture external ID
     * @returns List of broadcast links
     */
    async getBroadcastLinksByFixtureId(fixtureId: number): Promise<BroadcastLinkResponseDto[]> {
        // Validate fixtureId
        const fixture = await this.fixtureRepository.findOneBy({ externalId: fixtureId });
        if (!fixture) {
            throw new NotFoundException(`Fixture with externalId ${fixtureId} not found`);
        }

        const broadcastLinks = await this.broadcastLinkRepository.find({ where: { fixtureId } });
        this.logger.debug(`Fetched ${broadcastLinks.length} broadcast links for fixture ${fixtureId}`);
        return broadcastLinks.map(link => this.mapToResponseDto(link));
    }

    /**
     * Map BroadcastLink to response DTO
     * @param broadcastLink - BroadcastLink entity
     * @returns BroadcastLinkResponseDto
     */
    private mapToResponseDto(broadcastLink: BroadcastLink): BroadcastLinkResponseDto {
        return {
            id: broadcastLink.id,
            fixtureId: broadcastLink.fixtureId,
            linkName: broadcastLink.linkName,
            linkUrl: broadcastLink.linkUrl,
            addedBy: broadcastLink.addedBy,
            linkComment: broadcastLink.linkComment,
            createdAt: broadcastLink.createdAt.toISOString(),
            updatedAt: broadcastLink.updatedAt.toISOString(),
        };
    }
}