import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import { SWAGGER_CONSTANTS } from './swagger.constants';
import configuration from '../core/config/configuration';
import { ConfigType } from '@nestjs/config';

/**
 * Configures Swagger documentation for the APISportsGame API.
 * @param app NestJS application instance
 * @param config Configuration service
 */
export function setupSwagger(app: INestApplication, config: ConfigType<typeof configuration>) {
    const swaggerConfig = new DocumentBuilder()
        .setTitle(SWAGGER_CONSTANTS.TITLE)
        .setDescription(SWAGGER_CONSTANTS.DESCRIPTION)
        .setVersion(SWAGGER_CONSTANTS.VERSION)
        .setContact(
            SWAGGER_CONSTANTS.CONTACT_NAME,
            SWAGGER_CONSTANTS.CONTACT_URL,
            SWAGGER_CONSTANTS.CONTACT_EMAIL,
        )
        .addServer(config.feDomain, 'Production Server')
        .addServer('http://localhost:3000', 'Local Development')
        .addBearerAuth(
            { type: 'http', scheme: 'bearer', bearerFormat: 'JWT' },
            SWAGGER_CONSTANTS.AUTH_NAME,
        )
        .addTag(SWAGGER_CONSTANTS.TAGS.AUTH, 'Authentication endpoints - Login, logout, user management')
        .addTag(SWAGGER_CONSTANTS.TAGS.FOOTBALL, 'Football data endpoints - Fixtures, teams, leagues')
        .addTag(SWAGGER_CONSTANTS.TAGS.ADMIN, 'Admin management - User administration, system controls')
        .addTag(SWAGGER_CONSTANTS.TAGS.PUBLIC, 'Public endpoints - No authentication required')
        .addTag(SWAGGER_CONSTANTS.TAGS.SYNC, 'Data synchronization - Manual sync triggers, status')
        .build();

    const document = SwaggerModule.createDocument(app, swaggerConfig, {
        deepScanRoutes: true,
    });

    SwaggerModule.setup('api-docs', app, document, {
        customSiteTitle: SWAGGER_CONSTANTS.TITLE,
        customCss: `
      .swagger-ui .topbar { background-color: #1a3c34; }
      .swagger-ui .topbar a { color: #ffffff; }
    `,
        customfavIcon: '/public/images/favicon.ico',
        customJs: [],
        swaggerOptions: {
            persistAuthorization: true,
            tryItOutEnabled: true,
        },
    });
}