import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule } from './docs/swagger.module';

async function bootstrap() {

  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(new ValidationPipe({ transform: true }));

  // Cấu hình Swagger từ module riêng
  SwaggerModule.configure(app);
  console.log('APISportsGame started');
  await app.listen(3000);
}
bootstrap();