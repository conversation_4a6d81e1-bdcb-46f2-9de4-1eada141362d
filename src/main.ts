import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { setupSwagger } from './docs/swagger.config';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Global validation pipe
  app.useGlobalPipes(new ValidationPipe({
    transform: true,
    whitelist: true,
    forbidNonWhitelisted: true,
  }));

  // Get configuration
  const configService = app.get(ConfigService);
  const config = configService.get('app');

  // Setup Swagger documentation
  setupSwagger(app, config);

  console.log('APISportsGame started');
  console.log('📚 API Documentation available at: http://localhost:3000/api-docs');

  await app.listen(3000);
}
bootstrap();