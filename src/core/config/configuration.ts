import { registerAs } from '@nestjs/config';

interface AppConfig {
    apiFootballUrl: string;
    apiFootballKey: string;
    apiRetryAttempts: number;
    apiRetryDelay: number;
    redisHost: string;
    redisPort: number;
    redisPassword: string | undefined;
    dbHost: string;
    dbUser: string;
    dbPassword: string;
    dbName: string;
    dbPort: number;
    imageStoragePath: string;
    feDomain: string;
}

export const CONFIG_KEY = 'app';

export default registerAs(CONFIG_KEY, () => {
    const getEnv = (key: string, defaultValue?: string): string => {
        const value = process.env[key] ?? defaultValue;
        if (value === undefined) {
            throw new Error(`Missing required environment variable: ${key}`);
        }
        return value;
    };

    const config: AppConfig = {
        apiFootballUrl: getEnv('API_FOOTBALL_URL'),
        apiFootballKey: getEnv('API_FOOTBALL_KEY'),
        apiRetryAttempts: parseInt(getEnv('API_RETRY_ATTEMPTS', '3'), 10),
        apiRetryDelay: parseInt(getEnv('API_RETRY_DELAY', '1000'), 10),
        redisHost: getEnv('REDIS_HOST', '127.0.0.1'),
        redisPort: parseInt(getEnv('REDIS_PORT', '6379'), 10),
        redisPassword: process.env.REDIS_PASSWORD,
        dbHost: getEnv('DB_HOST', 'localhost'),
        dbUser: getEnv('DB_USER'),
        dbPassword: getEnv('DB_PASSWORD'),
        dbName: getEnv('DB_NAME'),
        dbPort: parseInt(getEnv('DB_PORT', '5432'), 10),
        imageStoragePath: getEnv('IMAGE_STORAGE_PATH', './public/images'),
        feDomain: getEnv('FE_DOMAIN'),
    };

    return config;
});