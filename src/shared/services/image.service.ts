import { Injectable, Inject, Logger } from '@nestjs/common';
import { ConfigType } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import configuration from '../../core/config/configuration';

@Injectable()
export class ImageService {
    constructor(
        @Inject(configuration.KEY)
        private config: ConfigType<typeof configuration>,
    ) { }

    /**
     * Download an image from a URL and save it locally if it doesn't exist, with retry logic
     * @param url - The URL of the image to download
     * @param type - The type of image (e.g., 'leagues', 'teams', 'flags')
     * @param fileName - The name of the file to save
     * @returns The file path of the saved or existing image
     */
    async downloadImage(url: string, type: string, fileName: string): Promise<string> {
        const folderPath = path.join(this.config.imageStoragePath, type);
        const filePath = path.join(folderPath, fileName);

        // Kiểm tra file đã tồn tại
        if (fs.existsSync(filePath)) {
            return filePath;
        }

        // Tạo thư mục nếu chưa tồn tại
        fs.mkdirSync(folderPath, { recursive: true });

        // Retry logic
        const maxRetries = 5;
        const retryDelay = 3000; // 1000ms

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                const writer = fs.createWriteStream(filePath);
                const response = await axios({
                    url,
                    method: 'GET',
                    responseType: 'stream',
                    timeout: 5000,
                });
                response.data.pipe(writer);
                return new Promise((resolve, reject) => {
                    writer.on('finish', () => resolve(filePath));
                    writer.on('error', reject);
                });
            } catch (error) {
                if (attempt === maxRetries) {
                    throw new Error(`Failed to download image ${url} after ${maxRetries} attempts: ${error.message}`);
                }
                // Log lỗi retry, đặc biệt với HTTP 429
                const status = error.response?.status;
                this.logger.warn(
                    `Attempt ${attempt} failed for ${url}: ${error.message}${status ? ` (HTTP ${status})` : ''}. Retrying after ${retryDelay}ms...`,
                );
                await new Promise((resolve) => setTimeout(resolve, retryDelay));
            }
        }

        throw new Error('Unexpected error in retry logic');
    }

    private readonly logger = new Logger(ImageService.name);
}