import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository } from 'typeorm';
import axios from 'axios';
import { FixtureStatisticsService } from '../../../src/sports/football/services/fixture-statistics.service';
import { FixtureStatistics } from '../../../src/sports/football/models/fixture-statistics.entity';
import { CacheService } from '../../../src/core/cache/cache.service';

jest.mock('axios');

describe('FixtureStatisticsService', () => {
    let service: FixtureStatisticsService;
    let statsRepository: Repository<FixtureStatistics>;
    let cacheService: CacheService;

    beforeEach(async () => {
        const module: TestingModule = await Test.createTestingModule({
            providers: [
                FixtureStatisticsService,
                {
                    provide: getRepositoryToken(FixtureStatistics),
                    useClass: Repository,
                },
                {
                    provide: ConfigService,
                    useValue: {
                        get: jest.fn().mockImplementation((key: string) => {
                            if (key === 'app.apiFootballUrl') return 'https://v3.football.api-sports.io';
                            if (key === 'app.apiFootballKey') return 'mock-key';
                            return undefined;
                        }),
                    },
                },
                {
                    provide: CacheService,
                    useValue: {
                        getCache: jest.fn(),
                        setCache: jest.fn(),
                    },
                },
            ],
        }).compile();

        service = module.get<FixtureStatisticsService>(FixtureStatisticsService);
        statsRepository = module.get<Repository<FixtureStatistics>>(getRepositoryToken(FixtureStatistics));
        cacheService = module.get<CacheService>(CacheService);
        jest.clearAllMocks();
    });

    it('should return statistics from cache', async () => {
        const externalId = 1354626;
        const cachedStats = [
            {
                fixtureId: externalId,
                teamName: 'Manchester United',
                statistics: {
                    shotsOnGoal: 5,
                    shotsOffGoal: 0,
                    totalShots: 0,
                    corners: 3,
                    offsides: 0,
                    yellowCards: 0,
                    redCards: 0,
                    possession: '0%',
                },
            },
        ];
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(JSON.stringify(cachedStats));

        const result = await service.getStatistics(externalId);

        expect(cacheService.getCache).toHaveBeenCalledWith(`fixture_stats_${externalId}`);
        expect(result).toEqual({ data: cachedStats, status: 200 });
    });

    it('should fetch statistics from API and save to DB', async () => {
        const externalId = 1354626;
        const apiData = [
            {
                team: { name: 'Manchester United' },
                statistics: [
                    { type: 'Shots on Goal', value: 5 },
                    { type: 'Corner Kicks', value: 3 },
                ],
            },
        ];
        const savedStats: FixtureStatistics[] = [
            {
                id: 1,
                fixtureId: externalId,
                teamName: 'Manchester United',
                statistics: {
                    shotsOnGoal: 5,
                    shotsOffGoal: 0,
                    totalShots: 0,
                    corners: 3,
                    offsides: 0,
                    yellowCards: 0,
                    redCards: 0,
                    possession: '0%',
                },
                createdAt: new Date('2025-05-14T10:00:00Z'),
                updatedAt: new Date('2025-05-14T10:00:00Z'),
            },
        ];
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(statsRepository, 'find').mockResolvedValueOnce([]);
        jest.spyOn(axios, 'get').mockResolvedValueOnce({ data: { response: apiData } });
        jest.spyOn(statsRepository, 'save').mockImplementationOnce(async () => savedStats as any);

        const result = await service.getStatistics(externalId);

        expect(axios.get).toHaveBeenCalledWith(
            'https://v3.football.api-sports.io/fixtures/statistics',
            expect.any(Object),
        );
        expect(statsRepository.save).toHaveBeenCalledWith(expect.any(Array));
        expect(cacheService.setCache).toHaveBeenCalledWith(
            `fixture_stats_${externalId}`,
            JSON.stringify([
                {
                    fixtureId: externalId,
                    teamName: 'Manchester United',
                    statistics: {
                        shotsOnGoal: 5,
                        shotsOffGoal: 0,
                        totalShots: 0,
                        corners: 3,
                        offsides: 0,
                        yellowCards: 0,
                        redCards: 0,
                        possession: '0%',
                    },
                },
            ]),
            3600,
        );
        expect(result).toEqual({
            data: [
                {
                    fixtureId: externalId,
                    teamName: 'Manchester United',
                    statistics: {
                        shotsOnGoal: 5,
                        shotsOffGoal: 0,
                        totalShots: 0,
                        corners: 3,
                        offsides: 0,
                        yellowCards: 0,
                        redCards: 0,
                        possession: '0%',
                    },
                },
            ],
            status: 200,
        });
    });

    it('should return empty array if no statistics available', async () => {
        const externalId = 1354626;
        jest.spyOn(cacheService, 'getCache').mockResolvedValueOnce(null);
        jest.spyOn(statsRepository, 'find').mockResolvedValueOnce([]);
        jest.spyOn(axios, 'get').mockResolvedValueOnce({ data: { response: [] } });
        jest.spyOn(statsRepository, 'save').mockImplementationOnce(async () => [] as any); // Thêm spy cho save

        const result = await service.getStatistics(externalId);

        expect(axios.get).toHaveBeenCalledWith(
            'https://v3.football.api-sports.io/fixtures/statistics',
            expect.any(Object),
        );
        expect(statsRepository.save).not.toHaveBeenCalled();
        expect(cacheService.setCache).not.toHaveBeenCalled();
        expect(result).toEqual({
            data: [],
            status: 200,
            message: `No statistics available for fixture ${externalId}`,
        });
    });
});