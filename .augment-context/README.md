# Augment Context System

**Purpose:** Maintain development context and prevent information loss during coding sessions.

## 📁 **File Structure**

```
.augment-context/
├── README.md                   # This file - system overview
├── current-session.md          # Active development session tracking
├── project-state.md           # Overall project state and completed features
├── next-tasks.md              # Planned tasks and implementation roadmap
├── memory-bank.md             # Key insights, patterns, and lessons learned
└── implementation-notes.md     # Technical decisions and code snippets
```

## 🎯 **File Purposes**

### **current-session.md**
- **Purpose:** Track current development session progress
- **Updates:** After each major task completion
- **Content:** Current focus, completed tasks, next steps, technical context

### **project-state.md**
- **Purpose:** High-level project overview and feature status
- **Updates:** When major features are completed
- **Content:** Architecture overview, completed features, technical stack, metrics

### **next-tasks.md**
- **Purpose:** Detailed implementation plan and task prioritization
- **Updates:** As tasks are completed or priorities change
- **Content:** Immediate tasks, detailed plans, testing strategy, success metrics

### **memory-bank.md**
- **Purpose:** Capture important insights and patterns for future reference
- **Updates:** When significant insights or patterns are discovered
- **Content:** Architecture insights, technical patterns, performance insights, lessons learned

### **implementation-notes.md**
- **Purpose:** Store technical decisions and code snippets
- **Updates:** During implementation of complex features
- **Content:** Code patterns, configuration examples, troubleshooting notes

## 🔄 **Update Strategy**

### **Automatic Updates**
The AI assistant will automatically update these files:

1. **After completing major tasks** → Update `current-session.md`
2. **When features are finished** → Update `project-state.md`
3. **When planning changes** → Update `next-tasks.md`
4. **When learning insights** → Update `memory-bank.md`
5. **During complex implementation** → Update `implementation-notes.md`

### **Manual Updates**
Developers can manually update files when:
- Changing project direction
- Adding new requirements
- Documenting external decisions
- Recording meeting outcomes

## 📋 **Usage Guidelines**

### **For AI Assistant:**
1. **Always reference** these files before starting new tasks
2. **Update immediately** after completing significant work
3. **Maintain consistency** in format and structure
4. **Cross-reference** between files for context

### **For Developers:**
1. **Review files** at start of development sessions
2. **Update priorities** in `next-tasks.md` as needed
3. **Add insights** to `memory-bank.md` when discovered
4. **Reference patterns** from previous implementations

## 🎯 **Benefits**

### **Context Preservation**
- No loss of important decisions or insights
- Smooth handoffs between development sessions
- Clear understanding of project evolution

### **Development Efficiency**
- Quick reference to established patterns
- Avoid repeating past mistakes
- Faster onboarding for new team members

### **Quality Assurance**
- Consistent implementation patterns
- Documented architectural decisions
- Traceable development history

## 🔧 **Maintenance**

### **File Size Management**
- Keep files under 300 lines for readability
- Archive old content when files get too large
- Use clear section headers for navigation

### **Content Quality**
- Use consistent markdown formatting
- Include dates for temporal context
- Provide clear, actionable information

### **Cross-References**
- Link related information between files
- Reference LogWorking/ documentation
- Connect to project documentation

## 🚀 **Integration with Development Workflow**

### **Session Start**
1. Review `current-session.md` for context
2. Check `next-tasks.md` for priorities
3. Reference `memory-bank.md` for patterns

### **During Development**
1. Update `current-session.md` progress
2. Add insights to `memory-bank.md`
3. Document decisions in `implementation-notes.md`

### **Session End**
1. Update all relevant files
2. Plan next session in `next-tasks.md`
3. Archive completed tasks

## 📊 **Success Metrics**

### **Context Retention**
- Ability to resume development without information loss
- Quick reference to past decisions and patterns
- Consistent implementation across sessions

### **Development Speed**
- Faster task completion due to clear planning
- Reduced time spent re-learning project structure
- Efficient pattern reuse

### **Code Quality**
- Consistent architectural patterns
- Proper documentation of decisions
- Maintainable and scalable implementations

---
**Note:** This context system is designed to enhance development efficiency and maintain project knowledge across sessions.
