.
├── .augment-context
│   ├── README.md
│   ├── current-session.md
│   ├── memory-bank.md
│   ├── next-tasks.md
│   └── project-state.md
├── .augment-rules.md
├── .env
├── .env.api.example
├── .env.worker.example
├── .gitignore
├── .vscode
│   └── settings.json
├── Dockerfile.api
├── Dockerfile.worker
├── README.md
├── docker-compose.yml
├── ecosystem.config.js
├── eslint.config.mjs
├── jest.config.js
├── nest-cli.json
├── package-lock.json
├── package.json
├── src
│   ├── app.controller.spec.ts
│   ├── app.controller.ts
│   ├── app.module.ts
│   ├── app.service.ts
│   ├── auth
│   │   ├── auth-api.module.ts
│   │   ├── auth.module.ts
│   │   ├── controllers
│   │   │   └── auth.controller.ts
│   │   ├── decorators
│   │   │   ├── auth.decorators.ts
│   │   │   └── rate-limit.decorators.ts
│   │   ├── dto
│   │   │   └── auth.dto.ts
│   │   ├── entities
│   │   │   ├── index.ts
│   │   │   ├── refresh-token.entity.ts
│   │   │   ├── registered-user.entity.ts
│   │   │   └── system-user.entity.ts
│   │   ├── guards
│   │   │   ├── jwt-auth.guard.ts
│   │   │   ├── rate-limit.guard.ts
│   │   │   └── roles.guard.ts
│   │   ├── services
│   │   │   ├── admin-seeder.service.ts
│   │   │   ├── audit-log.service.ts
│   │   │   ├── auth.service.ts
│   │   │   └── user.service.ts
│   │   ├── strategies
│   │   │   └── jwt.strategy.ts
│   │   └── types
│   │       └── auth.types.ts
│   ├── broadcast-links
│   │   ├── broadcast-link.controller.ts
│   │   ├── broadcast-link.dto.ts
│   │   ├── broadcast-link.entity.ts
│   │   ├── broadcast-link.module.ts
│   │   └── broadcast-link.service.ts
│   ├── core
│   │   ├── cache
│   │   │   └── cache.service.ts
│   │   ├── config
│   │   │   ├── api.configuration.ts
│   │   │   ├── configuration.ts
│   │   │   └── worker.configuration.ts
│   │   ├── constants
│   │   │   └── index.ts
│   │   ├── core-api.module.ts
│   │   ├── core-worker.module.ts
│   │   ├── core.module.ts
│   │   ├── database
│   │   │   └── database.service.ts
│   │   ├── index.ts
│   │   └── logger
│   │       └── logger.service.ts
│   ├── docs
│   │   ├── swagger.config.ts
│   │   ├── swagger.constants.ts
│   │   └── swagger.module.ts
│   ├── main.ts
│   ├── shared
│   │   ├── index.ts
│   │   ├── interfaces
│   │   │   └── api-response.interface.ts
│   │   ├── services
│   │   │   ├── image.service.ts
│   │   │   └── utils.service.ts
│   │   └── shared.module.ts
│   ├── sports
│   │   ├── football
│   │   │   ├── controllers
│   │   │   │   ├── fixture.controller.ts
│   │   │   │   ├── league.controller.ts
│   │   │   │   ├── team-statistics.controller.ts
│   │   │   │   └── team.controller.ts
│   │   │   ├── football-api.module.ts
│   │   │   ├── football-worker.module.ts
│   │   │   ├── football.module.ts
│   │   │   ├── index.ts
│   │   │   ├── models
│   │   │   │   ├── fixture-statistics.dto.ts
│   │   │   │   ├── fixture-statistics.entity.ts
│   │   │   │   ├── fixture.dto.ts
│   │   │   │   ├── fixture.entity.ts
│   │   │   │   ├── league.dto.ts
│   │   │   │   ├── league.entity.ts
│   │   │   │   ├── player.entity.ts
│   │   │   │   ├── schedule.dto.ts
│   │   │   │   ├── team-statistics.dto.ts
│   │   │   │   ├── team-statistics.entity.ts
│   │   │   │   ├── team.dto.ts
│   │   │   │   └── team.entity.ts
│   │   │   ├── season-sync.module.ts
│   │   │   ├── services
│   │   │   │   ├── fixture-statistics.service.ts
│   │   │   │   ├── fixture.service.ts
│   │   │   │   ├── league.service.ts
│   │   │   │   ├── season-sync.service.ts
│   │   │   │   ├── sync.service.ts
│   │   │   │   ├── team-statistics.service.ts
│   │   │   │   └── team.service.ts
│   │   │   ├── sync.module.ts
│   │   │   └── sync.processor.ts
│   │   └── index.ts
│   ├── worker-sync.module.ts
│   └── worker.ts
├── test
│   ├── broadcast-links
│   │   └── broadcast-link.service.spec.ts.txt
│   ├── core
│   │   ├── cache.service.spec.ts
│   │   └── logger.service.spec.ts
│   ├── setup.ts
│   ├── shared
│   │   ├── image.service.spec.ts
│   │   └── utils.service.spec.ts
│   └── sports
│       └── football
│           ├── fixture-statistics.service.spec.ts
│           ├── fixture.entity.spec.ts
│           ├── fixture.service.spec.ts
│           ├── league.e2e-spec.ts
│           ├── league.entity.spec.ts.txt
│           ├── league.service.spec.ts.txt
│           ├── registered-user.entity.spec.ts
│           ├── system-user.entity.spec.ts
│           └── team.entity.spec.ts
├── tsconfig.build.json
└── tsconfig.json
